<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tax Information - GigGenius</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #004AAD;
            --primary-pink: #CD208B;
            --yellow: #FFD700;
            --text-dark: #000000;
            --text-light: #FFFFFF;
            --text-gray: #666;
            --text-muted: #6b7280;
            --bg-light: #f8f9fa;
            --bg-white: #ffffff;
            --border-color: #e5e7eb;
            --success-color: #4caf50;
            --warning-color: #ff9800;
            --inactive-color: #666666;
            --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --radius-sm: 0.25rem;
            --radius-md: 0.375rem;
            --radius-lg: 0.5rem;
            --radius-full: 9999px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }

        body {
            background-color: var(--bg-light);
            color: var(--text-dark);
            line-height: 1.5;
            min-height: 100vh;
        }

        a {
            text-decoration: none;
            color: inherit;
        }

        /* Layout */
        .container {
            max-width: 2000px;
            margin: 0 auto;
            background-color: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .main-content {
            padding: 1.5rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .dashboard {
            display: flex;
            width: 100%;
            background-color: var(--bg-white);
            color: var(--text-dark);
        }

        /* Navbar Styles */
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            height: 5rem;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .navbar-left {
            display: flex;
            align-items: center;
            gap: 2rem;
        }

        .logo {
            display: flex;
            align-items: center;
            text-decoration: none;
        }

        .logo img {
            width: 3.5rem;
            height: 3.5rem;
            border-radius: 50%;
            border: 2px solid var(--primary-blue);
            object-fit: cover;
        }

        .logo h1 {
            font-size: 1.7rem;
            font-weight: bold;
            color: var(--primary-pink);
            margin-left: 0.5rem;
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }
        }

        .nav-links a {
            color: var(--primary-blue);
            text-decoration: none;
            font-size: 1.1rem;
            font-weight: 500;
        }

        .nav-links a:hover {
            color: var(--primary-pink);
        }

        .nav-dropdown {
            position: relative;
            display: inline-block;
        }

        .nav-dropbtn {
            font-weight: 500;
            font-size: 1.1rem;
            color: var(--primary-blue);
            background: none;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0;
        }

        .nav-dropbtn:hover {
            color: var(--primary-pink);
        }

        .nav-dropdown-content {
            display: none;
            position: absolute;
            background-color: #fff;
            min-width: 200px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            border-radius: 8px;
            z-index: 1001;
            top: 100%;
            left: 0;
        }

        .nav-dropdown-content a {
            color: var(--primary-blue);
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            font-size: 1rem;
        }

        .nav-dropdown-content a:hover {
            background-color: #f9f9f9;
            color: var(--primary-pink);
        }

        .nav-dropdown:hover .nav-dropdown-content {
            display: block;
        }

        /* Right section container */
        .right-section {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        /* Search container */
        .search-container {
            display: flex;
            align-items: center;
        }

        @media (max-width: 768px) {
            .search-container {
                display: none;
            }
        }

        .search-type-select {
            position: relative;
        }

        .search-type-button {
            height: 2.5rem;
            background: white;
            border: 1px solid var(--primary-blue);
            border-right: none;
            border-radius: 8px 0 0 8px;
            padding: 0 1rem;
            color: var(--primary-blue);
            font-size: 1rem;
            display: flex;
            align-items: center;
            cursor: pointer;
        }

        .search-bar {
            height: 2.5rem;
            display: flex;
            align-items: center;
            background: white;
            border: 1px solid var(--primary-blue);
            border-radius: 0 8px 8px 0;
            width: 200px;
        }

        .search-bar input {
            border: none;
            outline: none;
            padding: 0 0.5rem;
            width: 100%;
            height: 100%;
            font-size: 1rem;
        }

        .search-bar .icon {
            color: var(--primary-blue);
            padding: 0 0.5rem;
        }

        /* Auth buttons container */
        .auth-buttons {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        /* Notification icon */
        .notification-icon {
            position: relative;
            cursor: pointer;
        }

        .notification-icon i {
            font-size: 1.5rem;
            color: var(--primary-blue);
        }

        .notification-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background-color: var(--primary-pink);
            color: white;
            border-radius: 50%;
            padding: 0.1rem 0.4rem;
            font-size: 0.8rem;
            min-width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Profile button */
        .profile-button {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            cursor: pointer;
            border: 2px solid var(--primary-blue);
        }

        .profile-button img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* Profile dropdown */
        .profile-dropdown {
            position: relative;
            display: inline-block;
        }

        .profile-dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            top: 50px;
            background-color: #fff;
            min-width: 200px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            border-radius: 8px;
            z-index: 1001;
        }

        .profile-dropdown-content a {
            color: var(--primary-blue);
            padding: 12px 16px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1rem;
        }

        .profile-dropdown-content a i {
            width: 20px;
            text-align: center;
        }

        .profile-dropdown-content a:hover {
            background-color: #f9f9f9;
            color: var(--primary-pink);
        }

        .dropdown-divider {
            height: 1px;
            background-color: #eee;
            margin: 8px 0;
        }

        .logout-option {
            color: #dc3545 !important;
        }

        .logout-option:hover {
            background-color: #fff5f5 !important;
            color: #dc3545 !important;
        }

        /* Show dropdown on click */
        .profile-dropdown-content.show {
            display: block;
        }

        /* Mobile menu styles */
        .mobile-menu-btn {
            background: none;
            border: none;
            cursor: pointer;
            width: 40px;
            height: 40px;
            border-radius: 6px;
            display: none;
            align-items: center;
            justify-content: center;
        }

        @media (max-width: 768px) {
            .mobile-menu-btn {
                display: flex;
            }
        }

        .mobile-menu-btn:hover {
            background-color: #f3f4f6;
        }

        .mobile-menu {
            display: none;
            position: absolute;
            top: 60px;
            right: 1rem;
            background-color: white;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            width: 200px;
            z-index: 20;
            border: 1px solid #e5e7eb;
        }

        .mobile-menu.active {
            display: block;
        }

        .mobile-menu a {
            display: block;
            padding: 0.75rem 1rem;
            color: #333;
            font-size: 0.875rem;
            border-bottom: 1px solid #f3f4f6;
        }

        .mobile-menu a:last-child {
            border-bottom: none;
        }

        .mobile-menu a:hover {
            background-color: #f9fafb;
        }

        /* Footer Styles */
        footer {
            background: #003a8c;
            padding: 2rem 5%;
            align-items: center;
        }

        .footer-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 3rem;
        }

        .footer-column h3 {
            margin-bottom: 1rem;
            color: #ffffff;
        }

        .footer-column a {
            display: block;
            color: #ffffff;
            text-decoration: none;
            margin-bottom: 0.5rem;
            transition: text-decoration 0.3s ease;
        }

        .footer-column a:hover {
            text-decoration: underline;
        }

        .footer-bottom {
            color: #ffffff;
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 2rem 0;
            flex-wrap: wrap;
            font-family: 'Poppins', sans-serif;
        }

        .social-icons {
            display: flex;
            gap: 1rem;
            margin-left: 1rem;
        }

        .social-icons a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            margin: 0;
        }

        .social-icons .bi {
            font-size: 1.2rem;
            color: #ffffff;
            transition: transform 0.3s ease, color 0.3s ease;
        }

        .social-icons a:hover {
            background-color: #CD208B;
            transform: translateY(-3px);
        }

        @media (max-width: 768px) {
            .footer-links {
                flex-direction: column;
                gap: 0.5rem;
            }
        }

        @media (max-width: 480px) {
            .footer-content {
                grid-template-columns: 1fr;
            }
        }

        /* Sidebar styles */
        .sidebar {
            width: 220px;
            background-color: white;
            padding: 30px 0;
            flex-shrink: 0;
            border-right: 1px solid var(--border-color);
        }

        .sidebar-title {
            font-size: 26px;
            font-weight: 600;
            padding: 0 20px;
            color: var(--text-dark);
            margin-bottom: 30px;
        }

        .sidebar-menu {
            list-style: none;
        }

        .sidebar-menu li {
            padding: 10px 20px;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .sidebar-menu li:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .sidebar-menu li.active {
            border-left: 3px solid var(--primary-pink);
            background-color: rgba(255, 255, 255, 0.05);
            padding-left: 17px;
        }

        .main-content {
            flex-grow: 1;
            padding: 30px;
        }

        .page-title {
            font-size: 24px;
            margin-bottom: 15px;
        }

        .page-description {
            color: var(--text-dark);
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 25px;
            max-width: 800px;
        }

        .section {
            background-color: var(--bg-white);
            border-radius: 6px;
            margin-bottom: 25px;
            border: 1px solid var(--border-color);
            box-shadow: var(--shadow-sm);
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            border-bottom: 1px solid var(--border-color);
        }

        .section-title-container {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 500;
        }

        .status-badge {
            background-color: rgba(0, 58, 140, 0.15); /* Changed to match primary blue */
            color: var(--success-color);
            padding: 3px 10px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .edit-button {
            background-color: transparent;
            border: none;
            cursor: pointer;
        }

        .edit-button svg {
            width: 20px;
            height: 20px;
            color: var(--primary-pink);
        }

        .section-body {
            padding: 20px;
        }

        .section-description {
            color: var(--inactive-color);
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 20px;
        }

        .info-row {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 15px;
        }

        .info-column {
            flex: 1;
            min-width: 200px;
            margin-bottom: 15px;
        }

        .info-label {
            color: var(--inactive-color);
            font-size: 13px;
            margin-bottom: 5px;
        }

        .info-value {
            font-size: 14px;
        }

        .address-block {
            margin-top: 10px;
        }

        .address-value {
            line-height: 1.5;
        }

        .divider {
            height: 1px;
            background-color: var(--border-color);
            margin: 20px 0;
        }

        .link {
            color: var(--primary-pink);
            text-decoration: none;
        }

        .link:hover {
            text-decoration: underline;
        }

        /* Responsive styles */
        @media (max-width: 768px) {
            .dashboard {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                padding: 15px 0;
            }

            .main-content {
                padding: 15px;
            }

            .info-column {
                flex: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header Section -->
        <nav class="navbar">
            <div class="navbar-left">
                <a href="{{ url_for('landing_page') }}" class="logo">
                    <img src="{{ url_for('static', filename='img/logo.png') }}" alt="GigGenius Logo">
                    <h1>GigGenius</h1>
                </a>
                <div class="nav-links">
                    <a href="{{ url_for('genius_page') }}">Find Gigs</a>
                    <a href="{{ url_for('my_proposal') }}">Proposals</a>
                    <div class="nav-dropdown">
                        <button class="nav-dropbtn">Contracts <i class="bi bi-chevron-down"></i></button>
                        <div class="nav-dropdown-content">
                            <a href="{{ url_for('tracker') }}">Log Works</a>
                        </div>
                    </div>
                    <div class="nav-dropdown">
                        <button class="nav-dropbtn">Earnings <i class="bi bi-chevron-down"></i></button>
                        <div class="nav-dropdown-content">
                            <a href="{{ url_for('billing_and_earnings') }}">Billings and Earnings</a>
                            <a href="{{ url_for('withdraw_earnings') }}">Withdraw Earnings</a>
                            <a href="{{ url_for('tax_info') }}">Tax Info</a>
                        </div>
                    </div>
                    <a href="{{ url_for('landing_page') }}">Messages</a>
                </div>
            </div>
            <div class="right-section">
                <div class="search-container">
                    <div class="search-type-select">
                        <button id="searchTypeBtn" class="search-type-button">
                            <span id="selectedSearchType">Gigs</span>
                            <i class="bi bi-chevron-down"></i>
                        </button>
                    </div>
                    <div class="search-bar">
                        <input type="text" id="searchInput" placeholder="Search for gigs...">
                        <div class="icon">
                            <i class="bi bi-search"></i>
                        </div>
                    </div>
                </div>
                <div class="auth-buttons">
                    <div class="notification-icon">
                        <i class="bi bi-bell"></i>
                        <div class="notification-badge">3</div>
                    </div>
                    <div class="profile-dropdown">
                        <div class="profile-button">
                            <img src="{{ url_for('api_profile_photo', user_type='genius', user_id=session.get('user_id', 0)) }}" alt="Profile Picture"
                                 onerror="this.src=`https://ui-avatars.com/api/?name=${encodeURIComponent('{{ session.get('first_name', ' ')[0] }}{{ session.get('last_name', ' ')[0] }}')}&background=2563eb&color=fff&size=128`">
                        </div>
                        <div class="profile-dropdown-content">
                            <a href="{{ url_for('genius_profile') }}"><i class="bi bi-person"></i> Your Profile</a>
                            <a href="{{ url_for('withdraw_earnings', section='profile-settings') }}"><i class="bi bi-gear"></i> Settings</a>
                            <a href="#"><i class="bi bi-question-circle"></i> Help Center</a>
                            <div class="dropdown-divider"></div>
                            <a href="#" class="logout-option"><i class="bi bi-box-arrow-right"></i> Logout</a>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <div class="mobile-menu" id="mobileMenu">
            <a href="{{ url_for('genius_page') }}">Find Gigs</a>
            <a href="{{ url_for('my_proposal') }}">Proposals</a>
            <a href="#">Contracts</a>
            <a href="{{ url_for('tracker') }}">Log Works</a>
            <a href="{{ url_for('billing_and_earnings') }}">Billings and Earnings</a>
            <a href="{{ url_for('withdraw_earnings') }}">Withdraw Earnings</a>
            <a href="{{ url_for('tax_info') }}">Tax Info</a>
            <a href="{{ url_for('landing_page') }}">Messages</a>
            <a href="{{ url_for('genius_profile') }}">Profile</a>
        </div>

        <div class="main-content">
            <div class="dashboard">
        <div class="sidebar">
            <div class="sidebar-title">Taxes</div>

            <ul class="sidebar-menu">
                <li class="active">Tax information</li>
            </ul>
        </div>

        <div class="main-content">
            <h1 class="page-title">Tax information</h1>

            <p class="page-description">
                This information is required in order to confirm if you are a U.S. or non-U.S. taxpayer and whether or not Upwork is required to withhold taxes from your earnings. Add your tax information now to avoid delays in getting paid.
            </p>

            <div class="section">
                <div class="section-header">
                    <div class="section-title-container">
                        <div class="section-title">Tax residence</div>
                        <div class="status-badge">Completed</div>
                    </div>
                    <button class="edit-button">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                        </svg>
                    </button>
                </div>

                <div class="section-body">
                    <p class="section-description">
                        Your tax residence information is part of the Upwork W-9 or W-8 form process. This address will be displayed on invoices.
                    </p>

                    <div class="info-row">
                        <div class="info-column">
                            <div class="info-label">Country</div>
                            <div class="info-value">Philippines</div>
                        </div>

                        <div class="info-column">
                            <div class="info-label">Municipality</div>
                            <div class="info-value">Cavinti</div>
                        </div>

                        <div class="info-column">
                            <div class="info-label">Postal code</div>
                            <div class="info-value">4013</div>
                        </div>
                    </div>

                    <div class="address-block">
                        <div class="info-label">Address</div>
                        <div class="info-value address-value">
                            135 Callies St.<br>
                            Cavinti, Laguna
                        </div>
                    </div>
                </div>
            </div>

            <div class="section">
                <div class="section-header">
                    <div class="section-title-container">
                        <div class="section-title">Taxpayer identification</div>
                        <div class="status-badge">Completed</div>
                    </div>
                    <button class="edit-button">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                        </svg>
                    </button>
                </div>

                <div class="section-body">
                    <p class="section-description">
                        Your taxpayer identification information will be included as an Upwork W-9 or W-8 series substitute form.
                    </p>

                    <div class="info-row">
                        <div class="info-column">
                            <div class="info-label">Legal name of taxpayer</div>
                            <div class="info-value">Nash Esguerra</div>
                        </div>

                        <div class="info-column">
                            <div class="info-label">Country of citizenship</div>
                            <div class="info-value">Philippines</div>
                        </div>
                    </div>

                    <div class="info-row">
                        <div class="info-column">
                            <div class="info-label">Federal tax classification</div>
                            <div class="info-value">Individual</div>
                        </div>

                        <div class="info-column">
                            <div class="info-label">Date of birth</div>
                            <div class="info-value">10/11/1991</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="section">
                <div class="section-header">
                    <div class="section-title-container">
                        <div class="section-title">Tax identification number</div>
                        <div class="status-badge">Completed</div>
                    </div>
                    <button class="edit-button">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                        </svg>
                    </button>
                </div>

                <div class="section-body">
                    <p class="section-description">
                        Please provide your tax identification number (TIN). If you don't have a TIN, consult a local tax professional or your tax advisor. For more details, <a href="#" class="link">read our help article</a>.
                    </p>

                    <div class="info-row">
                        <div class="info-column">
                            <div class="info-label">Tax identification number submitted</div>
                            <div class="info-value">*******000</div>
                        </div>

                        <div class="info-column">
                            <div class="info-label">Date</div>
                            <div class="info-value">11/30/2022</div>
                        </div>
                    </div>

                    <div class="divider"></div>

                    <div class="info-row">
                        <div class="info-column">
                            <div class="info-label">Signer of certificate</div>
                            <div class="info-value">Nash Esguerra</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Handle sidebar menu item clicks
            const menuItems = document.querySelectorAll('.sidebar-menu li');
            menuItems.forEach(item => {
                item.addEventListener('click', function() {
                    menuItems.forEach(i => i.classList.remove('active'));
                    this.classList.add('active');

                    // In a real app, we would load the corresponding content here
                    if (this.textContent !== 'Tax information') {
                        alert('Navigating to: ' + this.textContent);
                    }
                });
            });

            // Handle edit buttons
            const editButtons = document.querySelectorAll('.edit-button');
            editButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const sectionTitle = this.closest('.section-header').querySelector('.section-title').textContent;
                    alert('Editing: ' + sectionTitle);
                });
            });

            // Handle links
            const links = document.querySelectorAll('.link');
            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    alert('Opening help article');
                });
            });
        });

        // Mobile Menu Toggle
        const mobileMenuBtn = document.getElementById('mobileMenuBtn');
        const mobileMenu = document.getElementById('mobileMenu');

        mobileMenuBtn.addEventListener('click', () => {
            mobileMenu.classList.toggle('active');
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', (event) => {
            if (!mobileMenuBtn.contains(event.target) && !mobileMenu.contains(event.target)) {
                mobileMenu.classList.remove('active');
            }
        });

        // Profile dropdown toggle
        const profileIcon = document.getElementById('profileIcon');
        const profileDropdown = document.getElementById('profileDropdown');

        profileIcon.addEventListener('click', (event) => {
            event.stopPropagation();
            profileDropdown.classList.toggle('active');

            // Close notification dropdown if open
            const notificationDropdown = document.getElementById('notificationDropdown');
            if (notificationDropdown && notificationDropdown.classList.contains('active')) {
                notificationDropdown.classList.remove('active');
            }
        });

        // Close dropdowns when clicking outside
        document.addEventListener('click', (event) => {
            // Close profile dropdown when clicking outside
            if (!event.target.closest('.profile-dropdown')) {
                if (profileDropdown) {
                    profileDropdown.classList.remove('active');
                }
            }

            // Close notification dropdown when clicking outside
            if (!event.target.closest('.notification-dropdown')) {
                const notificationDropdown = document.getElementById('notificationDropdown');
                if (notificationDropdown) {
                    notificationDropdown.classList.remove('active');
                }
            }
        });

        profileDropdown.addEventListener('click', (event) => {
            event.stopPropagation();
        });

        // Notification dropdown toggle
        const notificationBtn = document.getElementById('notificationBtn');
        const notificationDropdown = document.getElementById('notificationDropdown');
        const markAllReadBtn = document.getElementById('markAllRead');

        if (notificationBtn && notificationDropdown) {
            notificationBtn.addEventListener('click', function(event) {
                event.stopPropagation();
                notificationDropdown.classList.toggle('active');

                // Close profile dropdown if open
                if (profileDropdown && profileDropdown.classList.contains('active')) {
                    profileDropdown.classList.remove('active');
                }
            });

            notificationDropdown.addEventListener('click', (event) => {
                event.stopPropagation();
            });

            // Mark all notifications as read
            if (markAllReadBtn) {
                markAllReadBtn.addEventListener('click', function() {
                    const unreadNotifications = document.querySelectorAll('.notification-item.unread');
                    unreadNotifications.forEach(notification => {
                        notification.classList.remove('unread');
                    });

                    // Remove the notification indicator
                    const indicator = document.querySelector('.notification-indicator');
                    if (indicator) {
                        indicator.style.display = 'none';
                    }
                });
            }

            // Mark individual notification as read when clicked
            const notificationItems = document.querySelectorAll('.notification-item');
            notificationItems.forEach(item => {
                item.addEventListener('click', function() {
                    this.classList.remove('unread');

                    // Check if there are any unread notifications left
                    const unreadNotifications = document.querySelectorAll('.notification-item.unread');
                    if (unreadNotifications.length === 0) {
                        const indicator = document.querySelector('.notification-indicator');
                        if (indicator) {
                            indicator.style.display = 'none';
                        }
                    }
                });
            });
        }
    </script>

    </div> <!-- End of dashboard div -->
    </div> <!-- End of main-content div -->

        <!-- Footer -->
        <footer>
            <div class="footer-grid">
                <div class="footer-column">
                    <h3>For Clients</h3>
                    <a href="#">How to Hire</a>
                    <a href="#">Marketplace</a>
                    <a href="#">Payroll Services</a>
                    <a href="#">Service Catalog</a>
                    <a href="#">Business Networking</a>
                    <a href="#">PH Business Loan</a>
                </div>
                <div class="footer-column">
                    <h3>For Geniuses</h3>
                    <a href="#">How It Works?</a>
                    <a href="#">Why Can't I Apply?</a>
                    <a href="#">Direct Contracts</a>
                    <a href="#">Find Mentors</a>
                    <a href="#">Mentor Application</a>
                    <a href="#">PH Health Insurance</a>
                    <a href="#">PH Life Insurance</a>
                </div>
                <div class="footer-column">
                    <h3>Resources</h3>
                    <a href="#">Help & Support</a>
                    <a href="#">News & Events</a>
                    <a href="#">Affiliate Program</a>
                </div>
                <div class="footer-column">
                    <h3>Company</h3>
                    <a href="#">About Us</a>
                    <a href="#">Contact Us</a>
                    <a href="#">Charity Projects</a>
                </div>
            </div>
            <div class="footer-bottom">
                <p>
                    Follow Us:
                    <span class="social-icons">
                        <a href="https://www.facebook.com/giggenius.io"><i class="bi bi-facebook"></i></a>
                        <a href="https://www.instagram.com/giggenius.io/"><i class="bi bi-instagram"></i></a>
                        <a href="https://twitter.com/giggenius_io"><i class="bi bi-twitter-x"></i></a>
                        <a href="#"><i class="bi bi-tiktok"></i></a>
                        <a href="https://www.youtube.com/@giggenius"><i class="bi bi-youtube"></i></a>
                        <a href="https://www.linkedin.com/company/gig-genius/"><i class="bi bi-linkedin"></i></a>
                    </span>
                </p>
                <p>©2025 GigGenius by <a href="https://genuinelysolutions.com/">Genuinely Business Solutions</a></p>
                <p>
                    <a href="#">Terms of Service</a> |
                    <a href="#">Privacy Policy</a>
                </p>
            </div>
        </footer>
    </div> <!-- End of container -->

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Mobile Menu Toggle
            const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
            const mobileMenu = document.getElementById('mobileMenu');

            if (mobileMenuBtn && mobileMenu) {
                mobileMenuBtn.addEventListener('click', () => {
                    mobileMenu.classList.toggle('active');
                });

                // Close mobile menu when clicking outside
                document.addEventListener('click', (event) => {
                    if (!mobileMenuBtn.contains(event.target) && !mobileMenu.contains(event.target)) {
                        mobileMenu.classList.remove('active');
                    }
                });
            }

            // Search type dropdown
            const searchTypeBtn = document.getElementById('searchTypeBtn');
            const searchTypeDropdown = document.getElementById('searchTypeDropdown');
            const selectedSearchType = document.getElementById('selectedSearchType');
            const searchInput = document.getElementById('searchInput');

            if (searchTypeBtn && searchTypeDropdown) {
                // Toggle dropdown
                searchTypeBtn.addEventListener('click', function() {
                    searchTypeDropdown.classList.toggle('active');
                });

                // Close dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (!searchTypeBtn.contains(e.target) && !searchTypeDropdown.contains(e.target)) {
                        searchTypeDropdown.classList.remove('active');
                    }
                });
            }

            // Close dropdowns when clicking outside
            window.addEventListener('click', function(e) {
                if (!e.target.matches('.nav-dropbtn')) {
                    const dropdowns = document.getElementsByClassName('nav-dropdown-content');
                    for (let dropdown of dropdowns) {
                        if (dropdown.classList.contains('show')) {
                            dropdown.classList.remove('show');
                        }
                    }
                }
            });

            // Profile dropdown
            const profileButton = document.querySelector('.profile-button');
            const profileDropdownContent = document.querySelector('.profile-dropdown-content');

            if (profileButton && profileDropdownContent) {
                // Toggle dropdown on profile button click
                profileButton.addEventListener('click', function(e) {
                    e.stopPropagation();
                    profileDropdownContent.classList.toggle('show');
                });

                // Close dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (!e.target.closest('.profile-dropdown')) {
                        profileDropdownContent.classList.remove('show');
                    }
                });
            }

            // Handle sidebar menu item clicks
            const menuItems = document.querySelectorAll('.sidebar-menu li');
            menuItems.forEach(item => {
                item.addEventListener('click', function() {
                    menuItems.forEach(i => i.classList.remove('active'));
                    this.classList.add('active');

                    // In a real app, we would load the corresponding content here
                    if (this.textContent !== 'Tax information') {
                        alert('Navigating to: ' + this.textContent);
                    }
                });
            });

            // Handle edit buttons
            const editButtons = document.querySelectorAll('.edit-button');
            editButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const sectionTitle = this.closest('.section-header').querySelector('.section-title').textContent;
                    alert('Editing: ' + sectionTitle);
                });
            });

            // Handle links
            const links = document.querySelectorAll('.link');
            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    alert('Opening help article');
                });
            });
        });
    </script>
</body>
</html>
