<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Job Post - Step 2</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">
  <link rel="stylesheet" href="{{ url_for('static', filename='css/page2-2025.css') }}">
</head>
<body>

  <div class="container">
    <!-- Left side -->
    <div class="left-side">
      <div class="step">2/3 &nbsp; <strong>Category</strong></div>
      <div class="title">Choose your job category.</div>
      <div class="description">
        This helps your job post stand out to the right candidates. It’s the first thing they’ll see, so make it count!
      </div>
    </div>

    <!-- Right side -->
    <div class="right-side">
      <label for="job-title"><strong>Write a title for your job post</strong></label>
      <input type="text" id="job-title" placeholder="e.g. UX Designer for mobile app" oninput="toggleJobCategory(this.value)">

      <div class="examples">
        <strong>Example titles</strong>
        <ul>
          <li>UX/UI designer to bring website mockup and prototype to life</li>
          <li>Video editor needed to create whiteboard explainer video</li>
          <li>UX designer with e-commerce experience to support app development</li>
        </ul>
      </div>

      <div class="job-category">
        <strong>Job category</strong>
        <div id="selected-categories">
          <label><input type="radio" name="category" value="Graphic Design"> Graphic Design</label>
          <label><input type="radio" name="category" value="General Translation Services"> General Translation Services</label>
          <label><input type="radio" name="category" value="Social Media Marketing"> Social Media Marketing</label>
        </div>
        <div class="category-actions">
          <div class="see-all" onclick="openCategoryModal()">See all categories</div>
          <button class="next-button" onclick="goToPage3()">Next</button>
        </div>
      </div>

    </div>
  </div>

  <!-- Category Modal -->
  <div id="categoryModal" class="modal">
    <div class="modal-content">
      <span class="close" onclick="closeCategoryModal()">&times;</span>
      <h2>Change category</h2>

      <p class="label">Category</p>
      <select id="categoryDropdown" onchange="updateSpecialties()">
        <option value="accounting">Accounting & Consulting</option>
        <option value="admin">Admin Support</option>
        <option value="customer">Customer Service</option>
        <option value="data">Data Science & Analytics</option>
        <option value="design">Design & Creative</option>
        <option value="engineering">Engineering & Architecture</option>
        <option value="it">IT & Networking</option>
        <option value="legal">Legal</option>
        <option value="sales">Sales & Marketing</option>
        <option value="translation">Translation</option>
        <option value="development">Web, Mobile & Software Dev</option>
        <option value="writing">Writing</option>
      </select>

      <p class="label">Specialty</p>
      <select id="specialtyDropdown">
        <option value="brand">Brand Identity Design</option>
        <option value="logo">Logo Design</option>
        <option value="singing">Singing</option>
        <option value="acting">Acting</option>
        <option value="voice">Voice Talent</option>
        <option value="music">Music Performance</option>
        <option value="vfx">Visual Effects</option>
        <option value="2d">2D Animation</option>
        <option value="video-edit">Video Editing</option>
        <option value="video-prod">Video Production</option>
        <option value="ai-video">AI Video Generation & Editing</option>
        <option value="motion">Motion Graphics</option>
        <option value="videography">Videography</option>
        <option value="3d">3D Animation</option>
        <option value="presentation">Presentation Design</option>
        <option value="art-direction">Art Direction</option>
        <option value="ai-image">AI Image Generation & Editing</option>
        <option value="packaging">Packaging Design</option>
        <option value="creative">Creative Direction</option>
        <option value="image-edit">Image Editing</option>
        <option value="editorial">Editorial Design</option>
        <option value="graphic" selected>Graphic Design</option>
        <option value="cartoons">Cartoons & Comics</option>
        <option value="pattern">Pattern Design</option>
        <option value="fine-art">Fine Art</option>
        <option value="portraits">Portraits & Caricatures</option>
        <option value="illustration">Illustration</option>
        <option value="songwriting">Songwriting & Music Composition</option>
        <option value="music-prod">Music Production</option>
        <option value="audio-edit">Audio Editing</option>
        <option value="ai-audio">AI Speech & Audio Generation</option>
        <option value="audio-prod">Audio Production</option>
        <option value="product-design">Product & Industrial Design</option>
        <option value="jewelry">Jewelry Design</option>
        <option value="fashion">Fashion Design</option>
        <option value="nft">NFT Art</option>
        <option value="ar-vr">AR/VR Design</option>
        <option value="game">Game Art</option>
        <option value="product-photo">Product Photography</option>
        <option value="local-photo">Local Photography</option>
      </select>

      <div class="modal-buttons">
        <button onclick="closeCategoryModal()">Cancel</button>
        <button onclick="applyCategory()">Apply</button>
      </div>
    </div>
  </div>



  <script>
function goToPage3() {
    const jobTitle = document.getElementById('job-title').value;
    const selectedCategory = document.querySelector('input[name="category"]:checked');

    if (!jobTitle.trim()) {
        alert("Please enter a job title.");
        return;
    }

    if (!selectedCategory) {
        alert("Please select a job category.");
        return;
    }

    // Log the category being saved
    console.log("Selected category:", selectedCategory.value);

    // Create a form to submit the data
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '/job/save_page2';

    // Add title input
    const titleInput = document.createElement('input');
    titleInput.type = 'hidden';
    titleInput.name = 'title';
    titleInput.value = jobTitle;
    form.appendChild(titleInput);

    // Add category input - make sure we're using the correct value
    const categoryInput = document.createElement('input');
    categoryInput.type = 'hidden';
    categoryInput.name = 'job_category';
    categoryInput.value = selectedCategory.value;
    form.appendChild(categoryInput);

    // Debug: Log the form data before submitting
    console.log("Form data - title:", jobTitle, "category:", selectedCategory.value);

    document.body.appendChild(form);
    form.submit();
}


  </script>

</body>
</html>
