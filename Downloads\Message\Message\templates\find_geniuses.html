<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Find Geniuses</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #004AAD;
            --primary-pink: #CD208B;
            --yellow: #FFD700;
            --text-dark: #000000;
            --text-light: #FFFFFF;
            --text-gray: #666;
        }

        html {
            font-size: 16px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            transition: all 0.3s ease-in-out;
        }

        body {
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
        }

        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 1%;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            background: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .logo {
            display: flex;
            align-items: center;
            font-size: 1.8rem;
            font-weight: 600;
            color: var(--primary-pink);
        }

        .logo img {
            width: 80px;
            height: 80px;
        }

        .logo:hover {
            color: var(--primary-blue);
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            align-items: center;
            margin-left: 2rem;
        }

        .nav-links a {
            font-weight: 500;
            font-size: 1.2rem;  /* Bigger navigation links */
            color: var(--primary-blue);
            text-decoration: none;
            transition: color 0.3s ease;
            padding: 0.5rem 1rem;
        }

        .nav-links a:hover {
            color: var(--primary-pink);
        }

        .right-section {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .search-container {
            display: flex;
            height: 50px;  /* Taller search container */
            margin-right: 1rem;
        }

        .search-type-select {
            position: relative;
            height: 100%;
        }

        .search-type-button {
            height: 50px;  /* Taller search type button */
            background: white;
            border: 2px solid var(--primary-blue);
            border-right: none;
            border-radius: 8px 0 0 8px;
            padding: 0 1rem;
            color: var(--primary-blue);
            font-size: 1.2rem;  /* Bigger text */
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
        }

        .search-type-button:hover {
            color: var(--primary-pink);
            border-color: var(--primary-pink);
        }

        .search-type-button:after {
            content: '▼';
            font-size: 0.8rem;
        }

        .search-type-dropdown {
            position: absolute;
            top: 55px;  /* Adjusted for taller search bar */
            left: 0;
            background: white;
            border: 2px solid var(--primary-blue);
            border-radius: 8px;
            margin-top: 0.5rem;
            min-width: 120px;
            display: none;
            z-index: 1000;
        }

        .search-type-dropdown.active {
            display: block;
        }

        .search-type-option {
            padding: 1rem 1.5rem;  /* Bigger padding */
            cursor: pointer;
            color: var(--primary-blue);
        }

        .search-type-option:hover {
            background: #f5f5f5;
            color: var(--primary-pink);
        }

        .search-bar {
            height: 50px;
            display: flex;
            align-items: center;
            background: white;
            border: 2px solid var(--primary-blue);
            border-radius: 0 8px 8px 0;
            width: 200px;
        }

        .search-bar:hover {
            border-color: var(--primary-pink);
        }

        .search-bar input {
            border: none;
            outline: none;
            padding: 0 1rem;
            width: 100%;
            height: 100%;
            font-size: 1rem;
        }

        .search-bar .icon {
            color: var(--primary-blue);
            padding: 0 0.5rem;
            font-size: 1rem;
        }

        .search-bar:hover .icon {
            color: var(--primary-pink);
        }

        .auth-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
            position: relative;
        }

        .btn {
            padding: 0 1.5rem;  /* Adjusted padding */
            border-radius: 8px;  /* Match search bar border-radius */
            font-size: 1rem;    /* Standard font size */
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 120px;   /* Reduced minimum width */
            height: 50px;       /* Match search bar height */
        }

        .navbar .btn {
            min-width: 120px;
            height: 50px;       /* Match search bar height */
            padding: 0 1.5rem;
            font-size: 1rem;
        }

        .modal .btn {
            width: 100%;
            height: 50px;       /* Match search bar height */
            margin: 0.5rem 0;
        }

        .btn-primary {
            background: var(--primary-pink);
            border: 2px solid var(--primary-pink);
            color: var(--text-light);
        }

        .btn-outline {
            background: white;
            border: 2px solid var(--primary-blue);
            color: var(--primary-blue);
        }

        .btn-gigs {
            background: var(--primary-blue);
            border: 2px solid var(--primary-blue);
            color: var(--text-light);
        }

        .btn-primary:hover {
            background: white;
            border: 2px solid var(--primary-pink);
            color: var(--primary-pink);
            text-decoration: none;
        }

        .btn-outline:hover {
            background: var(--primary-blue);
            color: var(--text-light);
            text-decoration: none;
        }

        .btn-gigs:hover {
            background: white;
            color: var(--primary-blue);
        }

        .navbar .auth-buttons {
            gap: 0.8rem;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.4);
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background-color: var(--text-light);
            padding: 2rem;
            border-radius: 10px;
            width: 90%;
            max-width: 500px;
            position: relative;
            text-align: center;
        }

        .modal-content h2 {
            font-size: 24px;
            color: var(--primary-blue);
        }

        .modal-buttons {
            margin-top: 1.5rem;
            display: flex;
            justify-content: center;
            padding: 0 2rem; /* Add padding on sides */
        }

        .modal-buttons .btn {
            width: auto; /* Remove fixed width */
            min-width: 160px; /* Set reasonable min-width */
            max-width: 80%; /* Prevent button from being too wide */
            margin: 0 auto; /* Center the button */
        }

        .close {
            position: absolute;
            right: 1rem;
            top: 0.1rem;
            font-size: 2rem;
            cursor: pointer;
        }

        .role-selection {
            margin: 2rem 0;
        }

        .role-option {
            border: 2px solid var(--primary-blue);
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .role-option:hover {
            background-color: var(--text-light);
        }

        .role-option.selected {
            border-color: var(--primary-pink);
            background-color: var(--text-light);
        }

        .role-option input[type="radio"] {
            margin-right: 0.5rem;
        }

        .login-link {
            text-align: center;
            margin-top: 15px;
            font-size: 0.9rem;
            color: var(--text-gray);
        }

        .login-link a {
            color: var(--primary-blue);
            text-decoration: none;
            font-weight: 500;
        }

        .login-link a:hover {
            text-decoration: underline;
        }

        /* Content Styles */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        /* Hero Section Styles */
        .hero-section {
            background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('../static/img/fgenius1.jpg');
            background-size: cover;
            background-position: center;
            color: var(--text-light);
            padding: 80px 0;
            text-align: center;
            border-radius: 0 0 50px 50px;
            margin-bottom: 60px;
        }

        .hero-content {
            max-width: 800px;
            margin: 0 auto;
        }

        .hero-content h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 20px;
            line-height: 1.2;
        }

        .hero-content p {
            font-size: 1.2rem;
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .auth-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
        }

        /* Talent Categories Styles */
        .talent-categories {
            padding: 60px 0;
        }

        .talent-categories h2 {
            text-align: center;
            font-size: 2.2rem;
            margin-bottom: 40px;
            color: var(--primary-blue);
            position: relative;
        }

        .talent-categories h2::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background-color: var(--primary-pink);
            border-radius: 10px;
        }

        .category-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 30px;
        }

        .category-card {
            background-color: var(--text-light);
            border-radius: var(--radius);
            padding: 30px 20px;
            text-align: center;
            box-shadow: var(--shadow);
            cursor: pointer;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .category-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
        }

        .category-card i {
            font-size: 2.5rem;
            color: var(--primary-pink);
            margin-bottom: 15px;
        }

        .category-card h3 {
            font-size: 1.3rem;
            margin-bottom: 10px;
            color: var(--primary-blue);
        }

        .category-card p {
            color: var(--text-gray);
            font-size: 0.9rem;
        }

        /* Job Board Styles */
        .job-board {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 1500;
            overflow-y: auto;
            padding: 50px 0;
        }

        .job-board-container {
            background-color: var(--text-light);
            border-radius: var(--radius);
            width: 90%;
            max-width: 1000px;
            margin: 0 auto;
            padding: 30px;
            position: relative;
        }

        .job-board-close {
            position: absolute;
            top: 15px;
            right: 15px;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--text-gray);
        }

        .job-board-title {
            font-size: 2rem;
            margin-bottom: 30px;
            color: var(--primary-blue);
            text-align: center;
        }

        .genius-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 30px;
        }

        .genius-card {
            background-color: var(--bg-light);
            border-radius: var(--radius);
            overflow: hidden;
            box-shadow: var(--shadow);
        }

        .genius-image {
            height: 200px;
            background-color: #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .genius-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .genius-info {
            padding: 20px;
        }

        .genius-name {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 5px;
            color: var(--primary-blue);
        }

        .genius-title {
            color: var(--primary-pink);
            font-weight: 500;
            margin-bottom: 10px;
        }

        .genius-rating {
            display: flex;
            align-items: center;
            gap: 5px;
            margin-bottom: 10px;
        }

        .genius-rating i {
            color: var(--yellow);
        }

        .genius-description {
            color: var(--text-gray);
            margin-bottom: 15px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .read-more {
            display: inline-block;
            color: var(--primary-pink);
            font-weight: 500;
            margin-top: 10px;
            cursor: pointer;
        }

        .read-more:hover {
            text-decoration: underline;
        }

        /* Footer Styles */
        footer {
            background: var(--primary-blue);
            padding: 2rem 5% 2rem;
        }

        .footer-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 3rem;
            margin-bottom: 2rem;
        }
        .footer-column h3 {
            margin-bottom: 1rem;
            color: var(--text-light);
        }

        .footer-column a {
            display: block;
            color: var(--text-light);
            text-decoration: none;
            margin-bottom: 0.5rem;
            transition: text-decoration 0.3s ease;
        }

        .footer-column a:hover {
            text-decoration: underline;
        }

        .footer-bottom {
            color: var(--text-light);
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid #ddd;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 100px;
        }

        .footer-bottom a {
            color: var(--text-light);
            margin: 0 10px;
            text-decoration: none;
        }

        .footer-bottom a:hover {
            text-decoration: underline;
        }

        .footer-bottom .social-icons img {
            width: 24px;
            height: 24px;
            margin: 0 5px;
        }

        .social-icons .bi {
            font-size: 1.5rem;
            margin-right: 10px;
            border-radius: 50%;
            color: var(--text-light);
            transition: transform 0.3s ease, color 0.3s ease;
        }

        .social-icons .bi:hover {
            transform: scale(1.2);
            color: var(--primary-pink);
        }

        .login-modal-content {
            max-width: 400px;
            padding: 2rem;
        }

        .login-container {
            width: 100%;
        }

        .login-container h2 {
            text-align: center;
            color: var(--primary-blue);
            margin-bottom: 1.5rem;
        }

        .login-container form .btn-primary {
            width: auto;
        }

        .form-group {
            position: relative;
            margin-bottom: 1rem;
        }

        .form-group i {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-gray);
        }

        .form-group input {
            width: 100%;
            padding: 0.8rem 1rem 0.8rem 2.5rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }

        .checkbox-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .forgot-password-link {
            color: var(--primary-blue);
            text-decoration: none;
            font-size: 0.9rem;
        }

        .forgot-password-link:hover {
            text-decoration: underline;
        }

        .or-separator {
            text-align: center;
            margin: 1rem 0;
            position: relative;
        }

        .or-separator::before,
        .or-separator::after {
            content: '';
            position: absolute;
            top: 50%;
            width: 45%;
            height: 1px;
            background-color: #ddd;
        }

        .or-separator::before {
            left: 0;
        }

        .or-separator::after {
            right: 0;
        }

        .signup-link {
            text-align: center;
            margin-top: 15px;
            font-size: 0.9rem;
            color: var(--text-gray);
        }

        .signup-link a {
            color: var(--primary-blue);
            text-decoration: none;
            font-weight: 500;
        }

        .signup-link a:hover {
            text-decoration: underline;
        }

        .forgot-password-modal {
            max-width: 400px;
            padding: 2rem;
        }

        .forgot-password-modal h2 {
            color: var(--primary-blue);
            margin-bottom: 1rem;
        }

        .forgot-password-modal input {
            width: 100%;
            padding: 0.8rem;
            margin: 1rem 0;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .security-code-modal {
            max-width: 450px;
            padding: 2rem;
            text-align: center;
        }

        .security-code-modal h2 {
            color: var(--primary-blue);
            margin-bottom: 1rem;
        }

        .email-display {
            background-color: #f5f5f5;
            padding: 0.8rem;
            margin: 1rem 0;
            border-radius: 5px;
            font-weight: 500;
            word-break: break-all;
        }

        .security-code-inputs {
            display: flex;
            justify-content: center;
            gap: 8px;
            margin: 1.5rem 0;
        }

        .code-input {
            width: 40px;
            height: 50px;
            text-align: center;
            font-size: 1.5rem;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .code-input:focus {
            border-color: var(--primary-blue);
            outline: none;
        }

        .resend-code {
            text-align: center;
            margin-top: 15px;
            font-size: 0.9rem;
            color: var(--text-gray);
        }

        .resend-code a {
            color: var(--primary-blue);
            text-decoration: none;
            font-weight: 500;
        }

        .resend-code a:hover {
            text-decoration: underline;
        }
        #verificationModal .modal-content {
            max-width: 500px;
        }

        #verificationMessage {
            margin: 20px 0;
            font-size: 1.1rem;
            text-align: center;
            line-height: 1.5;
        }
    </style>
</head>
    <div id="joinModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h2>Join as Genius or Client</h2>
            <div id="roleMessage" style="color: red; display: none;">Please select a role!</div>
            <div class="role-selection">
                <div class="role-option" onclick="selectOption('genius')">
                    <input type="radio" name="role" id="geniusRole">
                    <label for="geniusRole">
                        <i class="bi bi-person"></i> I'm a Genius (Freelancer)
                    </label>
                </div>
                <div class="role-option" onclick="selectOption('client')">
                    <input type="radio" name="role" id="clientRole">
                    <label for="clientRole">
                        <i class="bi bi-briefcase"></i> I'm a Client (Business Owner)
                    </label>
                </div>
            </div>
            <div class="modal-buttons">
                <button onclick="continueToRegistration()" class="btn btn-primary">Continue</button>
            </div>
            <p class="login-link">
                Already have an account? <a href="javascript:void(0)" onclick="closeModal(); openLoginModal()">Log In</a>
            </p>
        </div>
    </div>

    <div id="verificationModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeVerificationModal()">&times;</span>
            <h2>Confirm Your Selection</h2>
            <div id="verificationMessage"></div>
            <div class="modal-buttons">
                <button onclick="proceedToRegistration()" class="btn btn-primary">Proceed</button>
                <button onclick="closeVerificationModal()" class="btn btn-outline">Go Back</button>
            </div>
        </div>
    </div>

    <div id="loginModal" class="modal">
        <div class="modal-content login-modal-content">
            <span class="close" onclick="closeLoginModal()">&times;</span>
            <div class="login-container">
                <h2>Login to GigGenius</h2>
                <div id="loginErrorMessage" style="color: red; margin-bottom: 1rem; display: none;"></div>
                <form id="loginForm" method="POST" action="{{ url_for('login') }}">
                    <div class="form-group">
                        <i class="bi bi-envelope"></i>
                        <input type="email" name="email" placeholder="Email" required>
                    </div>
                    <div class="form-group">
                        <i class="bi bi-lock"></i>
                        <input type="password" name="password" id="password" placeholder="Password" required>
                    </div>
                    <div class="checkbox-container">
                        <label>
                            <input type="checkbox" id="showPassword" onclick="togglePasswordVisibility()"> Show Password
                        </label>
                        <a href="javascript:void(0)" class="forgot-password-link" onclick="openForgotPasswordModal()">Forgot Password?</a>
                    </div>
                    <button type="submit" class="btn btn-primary">LOGIN</button>
                </form>
                <div class="or-separator">or</div>
                <button class="btn btn-outline" onclick="signInWithGoogle()">
                    <img src="{{ url_for('static', filename='img/lp3.png') }}" alt="Google" style="width: 20px; height: 20px; margin-right: 10px;">
                    Continue with Google
                </button>
                <div class="signup-link">
                    Don't have a GigGenius account? <a href="javascript:void(0)" onclick="closeLoginModal(); openModal();">Sign Up</a>
                </div>
            </div>
        </div>
    </div>

    <div id="forgotPasswordModal" class="modal">
        <div class="modal-content forgot-password-modal">
            <span class="close" onclick="closeForgotPasswordModal()">&times;</span>
            <h2>Forgot Password</h2>
            <p>Please enter your email address to reset your password.</p>
            <input type="email" id="forgotPasswordEmail" placeholder="Email" required>
            <button class="btn btn-primary" onclick="submitForgotPassword()">Submit</button>
        </div>
    </div>

    <div id="securityCodeModal" class="modal">
        <div class="modal-content security-code-modal">
            <span class="close" onclick="closeSecurityCodeModal()">&times;</span>
            <div class="text-center mb-4">
                <img src="{{ url_for('static', filename='img/giggenius_logo.jpg') }}" alt="GigGenius Logo" style="width: 80px; height: 80px; border-radius: 50%;">
            </div>
            <h2>Verify Security Code</h2>
            <p>We'll send a security code to your email for verification.</p>
            <div class="email-display" id="securityCodeEmail"></div>
            <div class="code-input-container" style="display: none;" id="codeInputContainer">
                <p>Enter the 6-digit code sent to your email:</p>
                <div class="security-code-inputs">
                    <input type="text" maxlength="1" class="code-input" data-index="1">
                    <input type="text" maxlength="1" class="code-input" data-index="2">
                    <input type="text" maxlength="1" class="code-input" data-index="3">
                    <input type="text" maxlength="1" class="code-input" data-index="4">
                    <input type="text" maxlength="1" class="code-input" data-index="5">
                    <input type="text" maxlength="1" class="code-input" data-index="6">
                </div>
                <div id="codeErrorMessage" style="color: red; margin-top: 0.5rem; display: none;"></div>
                <button class="btn btn-primary" onclick="verifySecurityCode()">Verify</button>
                <p class="resend-code">
                    Didn't receive the code? <a href="javascript:void(0)" onclick="resendSecurityCode()">Resend Code</a>
                </p>
            </div>
            <button class="btn btn-primary" id="sendCodeBtn" onclick="sendSecurityCode()">Send Code</button>
        </div>
    </div>

<body>
    <nav class="navbar">
        <div style="display: flex; align-items: center;">
            <a href="{{ url_for('landing_page') }}" style="text-decoration: none;">
                <div class="logo">
                    <img src="{{ url_for('static', filename='img/giggenius_logo.jpg') }}" alt="GigGenius Logo">
                    GigGenius
                </div>
            </a>
            <div class="nav-links">
                <a href="{{ url_for('landing_page') }}">Home</a>
                <a href="{{ url_for('find_geniuses') }}" class="active">Find Geniuses</a>
                <a href="{{ url_for('find_gigs') }}">Find Gigs</a>
                <a href="{{ url_for('about_us') }}">About Us</a>
            </div>
        </div>

        <div class="right-section">
            <div class="search-container">
                <div class="search-type-select">
                    <button class="search-type-button" id="searchTypeBtn">
                        <span id="selectedSearchType">All</span>
                    </button>
                    <div class="search-type-dropdown" id="searchTypeDropdown">
                        <div class="search-type-option" data-value="all">All</div>
                        <div class="search-type-option" data-value="genius">Genius</div>
                        <div class="search-type-option" data-value="gigs">Gigs</div>
                        <div class="search-type-option" data-value="projects">Projects</div>
                    </div>
                </div>
                <div class="search-bar">
                    <input type="text" id="searchInput" placeholder="Search...">
                    <i class="fas fa-search icon"></i>
                </div>
            </div>
            <div class="auth-buttons">
                <a href="javascript:void(0)" onclick="openLoginModal()" class="btn btn-outline">Log In</a>
                <a href="javascript:void(0)" onclick="openModal()" class="btn btn-primary">Join</a>
            </div>
        </div>
    </nav>

    <br>
    <!-- Main Content -->
    <div class="container">
        <!-- Hero Section -->
        <section class="hero-section">
            <div class="hero-content">
                <h1>Build Your Business with<br>Top GENIUSES</h1>
                <p>Post a GIG and Connect with Independent GENIUS Today!</p>
                <div class="auth-buttons">
                    <button class="btn btn-gigs" onclick="openModal()">Get Started</button>
                </div>
            </div>
        </section>

        <!-- Talent Categories Section -->
        <section class="talent-categories">
            <h2>Browse Skills by Category</h2>
            <div class="category-grid">
                <div class="category-card" onclick="openJobBoard('Development & IT')">
                    <i class="fas fa-code"></i>
                    <h3>Development & IT</h3>
                    <p>Web, Mobile, Software Development, AI/ML, DevOps</p>
                </div>

                <div class="category-card" onclick="openJobBoard('AI Services')">
                    <i class="fas fa-robot"></i>
                    <h3>AI Services</h3>
                    <p>Machine Learning, Data Science, AI Development</p>
                </div>

                <div class="category-card" onclick="openJobBoard('Design & Creative')">
                    <i class="fas fa-paint-brush"></i>
                    <h3>Design & Creative</h3>
                    <p>UI/UX, Graphic Design, Animation, Illustration</p>
                </div>

                <div class="category-card" onclick="openJobBoard('Sales & Marketing')">
                    <i class="fas fa-chart-line"></i>
                    <h3>Sales & Marketing</h3>
                    <p>Digital Marketing, Social Media, SEO, Lead Generation</p>
                </div>

                <div class="category-card" onclick="openJobBoard('Admin & Customer Support')">
                    <i class="fas fa-headset"></i>
                    <h3>Admin & Customer Support</h3>
                    <p>Virtual Assistance, Customer Service, Data Entry</p>
                </div>

                <div class="category-card" onclick="openJobBoard('Writing & Translation')">
                    <i class="fas fa-pen"></i>
                    <h3>Writing & Translation</h3>
                    <p>Content Writing, Copywriting, Translation Services</p>
                </div>

                <div class="category-card" onclick="openJobBoard('Finance & Accounting')">
                    <i class="fas fa-chart-line"></i>
                    <h3>Finance & Accounting</h3>
                    <p>Bookkeeping, Financial Analysis, Tax Consulting</p>
                </div>

                <div class="category-card" onclick="openJobBoard('HR & Training')">
                    <i class="fas fa-users"></i>
                    <h3>HR & Training</h3>
                    <p>Recruitment, Training Development, HR Consulting</p>
                </div>

                <div class="category-card" onclick="openJobBoard('Legal')">
                    <i class="fas fa-balance-scale"></i>
                    <h3>Legal</h3>
                    <p>Legal Consulting, Contract Review, Compliance</p>
                </div>

                <div class="category-card" onclick="openJobBoard('Engineering & Architecture')">
                    <i class="fas fa-drafting-compass"></i>
                    <h3>Engineering & Architecture</h3>
                    <p>CAD Design, Architecture, Engineering Services</p>
                </div>
            </div>
        </section>
    </div>

        <!-- Job Board Template -->
    <div class="job-board" id="jobBoard">
        <div class="job-board-container">
            <div class="job-board-close" onclick="closeJobBoard()">
                <i class="fas fa-times"></i>
            </div>
            <h2 class="job-board-title" id="jobBoardTitle">Category Geniuses</h2>

            <div class="genius-grid">
                <!-- Sample Genius Cards - These would be dynamically generated -->
                <div class="genius-card">
                    <div class="genius-image">
                        <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="John Developer">
                    </div>
                    <div class="genius-info">
                        <h3 class="genius-name">John Developer</h3>
                        <p class="genius-title">Full Stack Developer</p>
                        <div class="genius-rating">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star-half-alt"></i>
                            <span>4.5 (120 reviews)</span>
                        </div>
                        <p class="genius-description">Experienced full stack developer with 8+ years of experience in React, Node.js, and AWS. Specialized in building scalable web applications and e-commerce solutions.</p>
                        <span class="read-more" onclick="openModal()">Read More</span>
                    </div>
                </div>

                <div class="genius-card">
                    <div class="genius-image">
                        <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="Sarah Designer">
                    </div>
                    <div class="genius-info">
                        <h3 class="genius-name">Sarah Designer</h3>
                        <p class="genius-title">UI/UX Designer</p>
                        <div class="genius-rating">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <span>5.0 (87 reviews)</span>
                        </div>
                        <p class="genius-description">Creative UI/UX designer with a passion for creating beautiful, intuitive interfaces. Expertise in Figma, Adobe XD, and design systems. Worked with startups and enterprise clients.</p>
                        <span class="read-more" onclick="openModal()">Read More</span>
                    </div>
                </div>

                <div class="genius-card">
                    <div class="genius-image">
                        <img src="https://randomuser.me/api/portraits/men/67.jpg" alt="Michael AI">
                    </div>
                    <div class="genius-info">
                        <h3 class="genius-name">Michael AI</h3>
                        <p class="genius-title">AI Engineer</p>
                        <div class="genius-rating">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star-o"></i>
                            <span>4.0 (45 reviews)</span>
                        </div>
                        <p class="genius-description">AI engineer specializing in machine learning models and natural language processing. Experience with TensorFlow, PyTorch, and deploying ML models in production environments. Helped companies implement AI solutions for business optimization.</p>
                        <span class="read-more" onclick="openModal()">Read More</span>
                    </div>
                </div>

                <div class="genius-card">
                    <div class="genius-image">
                        <img src="https://randomuser.me/api/portraits/women/28.jpg" alt="Emily Marketer">
                    </div>
                    <div class="genius-info">
                        <h3 class="genius-name">Emily Marketer</h3>
                        <p class="genius-title">Digital Marketing Specialist</p>
                        <div class="genius-rating">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star-half-alt"></i>
                            <span>4.7 (93 reviews)</span>
                        </div>
                        <p class="genius-description">Results-driven digital marketer with expertise in SEO, SEM, and social media marketing. Proven track record of increasing organic traffic and conversion rates for e-commerce and SaaS companies.</p>
                        <span class="read-more" onclick="openModal()">Read More</span>
                    </div>
                </div>

                <div class="genius-card">
                    <div class="genius-image">
                        <img src="https://randomuser.me/api/portraits/men/52.jpg" alt="David Writer">
                    </div>
                    <div class="genius-info">
                        <h3 class="genius-name">David Writer</h3>
                        <p class="genius-title">Content Strategist</p>
                        <div class="genius-rating">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <span>4.9 (76 reviews)</span>
                        </div>
                        <p class="genius-description">Versatile content writer and strategist with a background in journalism. Specializes in creating engaging blog posts, website copy, and marketing materials that drive traffic and conversions.</p>
                        <span class="read-more" onclick="openModal()">Read More</span>
                    </div>
                </div>

                <div class="genius-card">
                    <div class="genius-image">
                        <img src="https://randomuser.me/api/portraits/women/62.jpg" alt="Lisa Support">
                    </div>
                    <div class="genius-info">
                        <h3 class="genius-name">Lisa Support</h3>
                        <p class="genius-title">Customer Support Specialist</p>
                        <div class="genius-rating">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star-o"></i>
                            <span>4.2 (58 reviews)</span>
                        </div>
                        <p class="genius-description">Dedicated customer support specialist with experience in handling complex customer inquiries and resolving issues efficiently. Proficient in CRM systems and support ticketing platforms.</p>
                        <span class="read-more" onclick="openModal()">Read More</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <footer>
        <div class="footer-grid">
            <div class="footer-column">
                <h3>For Clients</h3>
                <a href="{{ url_for('how_to_hire') }}">How to Hire</a>
                <a href="{{ url_for('marketplace') }}">Marketplace</a>
                <a href="{{ url_for('accounting_services') }}">Payroll Services</a>
                <a href="{{ url_for('marketplace') }}">Service Catalog</a>
                <a href="{{ url_for('ph_business_loan') }}">Business Networking</a>
                <a href="{{ url_for('ph_business_loan') }}">PH Business Loan</a>
            </div>
            <div class="footer-column">
                <h3>For Geniuses</h3>
                <a href="{{ url_for('how_it_works') }}">How It Works?</a>
                <a href="{{ url_for('why_cant_apply') }}">Why Can't I Apply?</a>
                <a href="{{ url_for('direct_contracts') }}">Direct Contracts</a>
                <a href="{{ url_for('find_mentors') }}">Find Mentors</a>
                <a href="{{ url_for('mentor_application') }}">Mentor Application</a>
                <a href="{{ url_for('ph_health_insurance') }}">PH Health Insurance</a>
                <a href="{{ url_for('ph_life_insurance') }}">PH Life Insurance</a>
            </div>
            <div class="footer-column">
                <h3>Resources</h3>
                <a href="{{ url_for('help_and_support') }}">Help & Support</a>
                <a href="{{ url_for('news_and_events') }}">News & Events</a>
                <a href="{{ url_for('affiliate_program') }}">Affiliate Program</a>
            </div>
            <div class="footer-column">
                <h3>Company</h3>
                <a href="{{ url_for('about_us') }}">About Us</a>
                <a href="{{ url_for('contact_us') }}">Contact Us</a>
                <a href="{{ url_for('charity_projects') }}">Charity Projects</a>
            </div>
        </div>
        <div class="footer-bottom">
            <p>Follow Us:
                <span class="social-icons">
                    <a href="https://www.facebook.com/giggenius.io" class="bi bi-facebook"></a>
                    <a href="https://www.instagram.com/giggenius.io/" class="bi bi-instagram"></a>
                    <a href="https://twitter.com/giggenius_io" class="bi bi-twitter-x"></a>
                    <a href="https://www.tiktok.com/@giggenius.io" class="bi bi-tiktok"></a>
                    <a href="https://www.youtube.com/@giggenius" class="bi bi-youtube"></a>
                    <a href="https://www.linkedin.com/company/gig-genius/" class="bi bi-linkedin"></a>
                </span>
            </p>
            <p>©2025 GigGenius by<a href="https://genuinelysolutions.com/">Genuinely Business Solutions</a></p>
            <p>
                <a href="{{ url_for('terms_of_service') }}">Terms of Service</a> |
                <a href="{{ url_for('privacy_policy') }}">Privacy Policy</a>
            </p>
        </div>
    </footer>

    <script>
        function toggleMenu() {
            const navLinks = document.querySelector('.nav-links');
            navLinks.classList.toggle('active');
        }
        let selectedRole = null;

        function closeAllModals() {
            // Get all elements with class "modal" and hide them
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                modal.style.display = 'none';
            });
            // Restore scrolling
            document.body.style.overflow = 'auto';
        }

        function openModal() {
            closeAllModals(); // Close any open modals first
            document.getElementById('joinModal').style.display = 'flex';
            document.body.style.overflow = 'hidden'; // Prevent background scrolling
        }

        function closeModal() {
            document.getElementById('joinModal').style.display = 'none';
            document.body.style.overflow = 'auto'; // Restore scrolling
            document.getElementById('roleMessage').style.display = 'none';
            document.querySelectorAll('.role-option').forEach(option => {
                option.classList.remove('selected');
            });
            document.getElementById('geniusRole').checked = false;
            document.getElementById('clientRole').checked = false;
        }

        function selectOption(role) {
            document.querySelectorAll('.role-option').forEach(option => {
                option.classList.remove('selected');
            });

            event.currentTarget.classList.add('selected');
            document.getElementById('roleMessage').style.display = 'none';

            if (role === 'genius') {
                document.getElementById('geniusRole').checked = true;
                document.getElementById('clientRole').checked = false;
            } else {
                document.getElementById('clientRole').checked = true;
                document.getElementById('geniusRole').checked = false;
            }

        }

        function continueToRegistration() {
            showVerificationModal();
        }

        function openLoginModal() {
            closeAllModals(); // Close any open modals first
            document.getElementById('loginModal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }

        function closeLoginModal() {
            document.getElementById('loginModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function openForgotPasswordModal() {
            closeAllModals(); // Close any open modals first
            document.getElementById('forgotPasswordModal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }

        function closeForgotPasswordModal() {
            document.getElementById('forgotPasswordModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function togglePasswordVisibility() {
            const passwordInput = document.getElementById('password');
            passwordInput.type = passwordInput.type === 'password' ? 'text' : 'password';
        }

        function submitForgotPassword() {
            const email = document.getElementById('forgotPasswordEmail').value;
            // Add your forgot password logic here
            closeForgotPasswordModal();
        }

        window.onclick = function(event) {
            const joinModal = document.getElementById('joinModal');
            const loginModal = document.getElementById('loginModal');
            const forgotPasswordModal = document.getElementById('forgotPasswordModal');

            if (event.target === joinModal) {
                closeModal();
            }
            if (event.target === loginModal) {
                closeLoginModal();
            }
            if (event.target === forgotPasswordModal) {
                closeForgotPasswordModal();
            }
        }

        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        document.addEventListener('DOMContentLoaded', function() {
        const searchTypeBtn = document.getElementById('searchTypeBtn');
        const searchTypeDropdown = document.getElementById('searchTypeDropdown');
        const selectedSearchType = document.getElementById('selectedSearchType');
        const searchInput = document.getElementById('searchInput');
        const options = document.querySelectorAll('.search-type-option');

        // Toggle dropdown
        searchTypeBtn.addEventListener('click', function() {
            searchTypeDropdown.classList.toggle('active');
        });

        // Handle option selection
        options.forEach(option => {
            option.addEventListener('click', function() {
                const value = this.dataset.value;
                selectedSearchType.textContent = this.textContent;
                searchTypeDropdown.classList.remove('active');

                // Update placeholder based on selection
                const placeholders = {
                    genius: 'Search for genius...',
                    gigs: 'Search for gigs...',
                    projects: 'Search for projects...',
                    all: 'Search...'
                };
                searchInput.placeholder = placeholders[value] || placeholders.all;
            });
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.search-type-select')) {
                searchTypeDropdown.classList.remove('active');
            }
        });
        });
    document.getElementById('loginForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);

        fetch("{{ url_for('login') }}", {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (data.requireVerification) {
                    // Show security code verification modal for admin
                    openSecurityCodeModal();
                    // Display the email in the verification modal
                    document.getElementById('securityCodeEmail').textContent = data.email;
                    // Set a flag to indicate this is admin verification
                    window.selectedRole = null; // Clear any role selection
                    window.isAdminVerification = true;
                } else {
                    // Regular user redirect
                    window.location.href = data.redirect;
                }
            } else {
                // Show error message
                const errorMessage = document.getElementById('loginErrorMessage');
                errorMessage.textContent = data.error;
                errorMessage.style.display = 'block';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            const errorMessage = document.getElementById('loginErrorMessage');
            errorMessage.textContent = "An error occurred during login. Please try again.";
            errorMessage.style.display = 'block';
        });
    });

    function openSecurityCodeModal() {
        closeAllModals(); // Close any open modals first
        document.getElementById('securityCodeModal').style.display = 'flex';
        document.body.style.overflow = 'hidden';
        document.getElementById('codeInputContainer').style.display = 'none';
        document.getElementById('sendCodeBtn').style.display = 'block';
    }

    function closeSecurityCodeModal() {
        document.getElementById('securityCodeModal').style.display = 'none';
        document.body.style.overflow = 'auto';
    }

    function sendSecurityCode() {
        // Skip actual code sending and just show the code input
        document.getElementById('sendCodeBtn').style.display = 'none';
        document.getElementById('codeInputContainer').style.display = 'block';

        // Focus on first input
        document.querySelector('.code-input[data-index="1"]').focus();

        // Add event listeners for code inputs
        setupCodeInputs();
    }

    function setupCodeInputs() {
        const inputs = document.querySelectorAll('.code-input');

        inputs.forEach((input, index) => {
            input.addEventListener('keyup', function(e) {
                // If a number is entered
                if (/^[0-9]$/.test(e.key)) {
                    // Move to next input if available
                    if (index < inputs.length - 1) {
                        inputs[index + 1].focus();
                    }
                }
                // Handle backspace
                else if (e.key === 'Backspace') {
                    // Move to previous input if available and current is empty
                    if (index > 0 && input.value === '') {
                        inputs[index - 1].focus();
                    }
                }
            });

            // Handle paste event
            input.addEventListener('paste', function(e) {
                e.preventDefault();
                const pastedData = e.clipboardData.getData('text');
                if (/^\d+$/.test(pastedData)) {
                    // Fill inputs with pasted digits
                    for (let i = 0; i < Math.min(pastedData.length, inputs.length); i++) {
                        inputs[i].value = pastedData[i];
                    }
                    // Focus on the next empty input or the last one
                    const nextEmptyIndex = Array.from(inputs).findIndex(input => !input.value);
                    if (nextEmptyIndex !== -1) {
                        inputs[nextEmptyIndex].focus();
                    } else {
                        inputs[inputs.length - 1].focus();
                    }
                }
            });
        });
    }

    function verifySecurityCode() {
        // Get email from the form
        let email;
        const emailInput = document.getElementById('registrationEmail');
        if (emailInput) {
            email = emailInput.value;
        } else {
            email = document.getElementById('securityCodeEmail').textContent;
        }

        // Get the code from inputs
        const codeInputs = document.querySelectorAll('.code-input');
        let securityCode = '';
        codeInputs.forEach(input => {
            securityCode += input.value || '0'; // Use '0' for any empty input
        });

        // Create form data for verification
        const formData = new FormData();
        formData.append('email', email);
        formData.append('code', securityCode);

        // Determine which endpoint to use based on context
        const isAdminVerification = window.isAdminVerification === true;
        const endpoint = isAdminVerification ? "{{ url_for('verify_admin') }}" : "{{ url_for('verify_email') }}";

        // Send verification request to server
        fetch(endpoint, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (isAdminVerification) {
                    // Admin verification successful - redirect to admin page
                    window.location.href = data.redirect;
                } else {
                    // Registration verification - proceed to registration
                    closeSecurityCodeModal();
                    // Redirect to appropriate registration page
                    if (window.selectedRole === 'genius') {
                        window.location.href = "{{ url_for('genius_registration') }}";
                    } else {
                        window.location.href = "{{ url_for('client_registration') }}";
                    }
                }
            } else {
                // For testing purposes, proceed anyway
                if (isAdminVerification) {
                    window.location.href = "{{ url_for('admin_page') }}";
                } else {
                    closeSecurityCodeModal();
                    if (window.selectedRole === 'genius') {
                        window.location.href = "{{ url_for('genius_registration') }}";
                    } else {
                        window.location.href = "{{ url_for('client_registration') }}";
                    }
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            // For testing purposes, proceed anyway
            if (window.isAdminVerification) {
                window.location.href = "{{ url_for('admin_page') }}";
            } else {
                closeSecurityCodeModal();
                if (window.selectedRole === 'genius') {
                    window.location.href = "{{ url_for('genius_registration') }}";
                } else {
                    window.location.href = "{{ url_for('client_registration') }}";
                }
            }
        });
    }

    function resendSecurityCode() {
        // Reset the code inputs
        document.querySelectorAll('.code-input').forEach(input => {
            input.value = '';
        });

        // Hide error message if shown
        document.getElementById('codeErrorMessage').style.display = 'none';

        // Show sending state
        const resendLink = document.querySelector('.resend-code a');
        const originalText = resendLink.textContent;
        resendLink.textContent = 'Sending...';
        resendLink.style.pointerEvents = 'none';

        setTimeout(() => {
            // Restore link text
            resendLink.textContent = originalText;
            resendLink.style.pointerEvents = 'auto';

            // Focus on first input
            document.querySelector('.code-input[data-index="1"]').focus();
        }, 1500);
    }

    function closeVerificationModal() {
        document.getElementById('verificationModal').style.display = 'none';
    }

    function showVerificationModal() {
        const geniusRole = document.getElementById('geniusRole').checked;
        const clientRole = document.getElementById('clientRole').checked;
        const roleMessage = document.getElementById('roleMessage');

        if (!geniusRole && !clientRole) {
            roleMessage.style.display = 'block';
            return;
        }

        roleMessage.style.display = 'none';
        const verificationMessage = document.getElementById('verificationMessage');

        if (geniusRole) {
            verificationMessage.innerHTML = 'You are about to register as a <strong>Genius (Freelancer)</strong>. Is this correct?';
        } else if (clientRole) {
            verificationMessage.innerHTML = 'You are about to register as a <strong>Client (Business Owner)</strong>. Is this correct?';
        }

        document.getElementById('joinModal').style.display = 'none';
        document.getElementById('verificationModal').style.display = 'flex';
    }

    function proceedToRegistration() {
        const geniusRole = document.getElementById('geniusRole').checked;
        const clientRole = document.getElementById('clientRole').checked;

        // Show security code verification before proceeding to registration
        if (geniusRole || clientRole) {
            // Store the selected role in a variable for later use
            window.selectedRole = geniusRole ? 'genius' : 'client';

            // Close verification modal and open security code modal
            document.getElementById('verificationModal').style.display = 'none';
            openRegistrationSecurityModal();
        }
    }

    function openRegistrationSecurityModal() {
        closeAllModals(); // Close any open modals first
        document.getElementById('securityCodeModal').style.display = 'flex';
        document.body.style.overflow = 'hidden';
        document.getElementById('codeInputContainer').style.display = 'none';
        document.getElementById('sendCodeBtn').style.display = 'block';

        // Update modal title and description for registration context
        document.querySelector('#securityCodeModal h2').textContent = 'Verify Your Email';
        document.querySelector('#securityCodeModal p').textContent = 'We\'ll send a security code to verify your email before registration.';

        // Get email from the join modal if available
        const emailInput = document.querySelector('#joinModal input[type="email"]');
        if (emailInput && emailInput.value) {
            document.getElementById('securityCodeEmail').textContent = emailInput.value;
        } else {
            // If no email is available, show an input field
            document.getElementById('securityCodeEmail').innerHTML = '<input type="email" id="registrationEmail" placeholder="Enter your email" required>';
        }
    }

            // Job Board Functions
            function openJobBoard(category) {
            const jobBoard = document.getElementById('jobBoard');
            const jobBoardTitle = document.getElementById('jobBoardTitle');

            jobBoardTitle.textContent = category + ' Geniuses';
            jobBoard.style.display = 'block';

            // In a real application, you would fetch geniuses based on the category
            // and dynamically populate the genius-grid
        }

        function closeJobBoard() {
            document.getElementById('jobBoard').style.display = 'none';
        }

        // Close modals when clicking outside
        window.addEventListener('click', (e) => {
            const modal = document.getElementById('joinModal');
            if (e.target === modal) {
                closeModal();
            }

            const jobBoard = document.getElementById('jobBoard');
            if (e.target === jobBoard) {
                closeJobBoard();
            }
        });
    </script>
</body>
</html>
