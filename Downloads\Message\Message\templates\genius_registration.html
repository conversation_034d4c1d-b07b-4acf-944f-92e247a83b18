<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Genius Registration | GigGenius</title>
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='img/logo.png') }}">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #004AAD;
            --primary-pink: #CD208B;
            --yellow: #FFD700;
            --text-dark: #000000;
            --text-light: #FFFFFF;
            --text-gray: #666;
        }

        html {
            font-size: 1rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            transition: all 0.3s ease-in-out;
        }

        /* Body Styles */
        body {
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-pink) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            line-height: 1.6;
        }

        /* Logo Styles */
        .logo-container {
            display: flex;
            justify-content: center;
            padding: 0;
        }

        .logo-container a {
            display: flex;
            align-items: center;
            text-decoration: none;
        }

        .logo-container img {
            width: 100px;
            height: auto;
        }

        .header h1 {
            color: var(--primary-blue);
            font-size: 2.3rem;
            text-align: center;
            margin-top: 0.5rem;
        }

        .header p {
            color: var(--primary-pink);
            font-size: 1.5rem;
        }

        /* Container Styles */
        .container {
            max-width: 1000px;
            width: 100%;
            background: white;
            padding: 40px;
            border-radius: 16px;
            box-shadow: 0 4px 6px -1px rgba(0,0,0,0.1), 0 2px 4px -1px rgba(0,0,0,0.06);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .subtitle {
            color: var(--primary-pink);
            font-size: 1.1rem;
        }

        .profile-section {
            text-align: center;
            margin-bottom: 40px;
        }

        .profile-photo {
            width: 180px;
            height: 180px;
            margin: 0 auto;
            border-radius: 50%;
            background: var(--background-light);
            border: 2px dashed var(--primary-blue);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .profile-photo:hover {
            border-color: var(--primary-pink);
            background: #f0f7ff;
        }

        .profile-photo span {
            color: var(--primary-blue);
            font-size: 0.9rem;
            font-weight: 500;
            padding: 0 20px;
            text-align: center;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            color: var(--text-dark);
            font-weight: 500;
            font-size: 0.95rem;
        }

        input, select {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid var(--primary-blue);
            background-color: #f8f9fa;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            color: var(--text-dark);
        }

        input:hover, select:hover {
            border-color: var(--primary-pink);
        }

        input:focus, select:focus {
            border-color: var(--primary-pink);
            outline: none;
            box-shadow: 0 0 0 3px rgba(205, 32, 139, 0.1);
            background-color: white;
        }

        .error-container {
            background-color: #fee2e2;
            border: 1px solid red;
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
        }

        .error-message {
            color: red;
            font-size: 0.875rem;
        }

        .error-message strong {
            display: block;
            margin-bottom: 4px;
        }

        .error-message ul {
            margin-left: 20px;
            margin-top: 5px;
        }

        .checkbox-group {
            display: flex;
            align-items: flex-start;
            gap: 5px;
            margin: 0;
        }

        .checkbox-group input[type="checkbox"] {
            appearance: none;
            -webkit-appearance: none;
            width: 15px;
            height: 15px;
            border: 2px solid var(--primary-blue);
            border-radius: 4px;
            cursor: pointer;
            position: relative;
            margin: 0;
            transition: all 0.2s ease;
        }

        .checkbox-group input[type="checkbox"]:checked {
            background-color: var(--primary-pink);
            border-color: var(--primary-pink);
        }

        .checkbox-group input[type="checkbox"]:checked::after {
            content: '✓';
            position: absolute;
            color: white;
            font-size: 15px;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .checkbox-group input[type="checkbox"]:hover {
            border-color: var(--primary-pink);
        }

        .checkbox-group input[type="checkbox"]:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(205, 32, 139, 0.1);
        }

        .checkbox-group label {
            flex: 1;
            font-size: 0.95rem;
            line-height: 1.5;
            cursor: pointer;
            color: var(--text-dark);
            padding-top: 2px;
        }

        .checkbox-group a {
            color: var(--primary-blue);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .checkbox-group a:hover {
            color: var(--primary-pink);
            text-decoration: underline;
        }

        /* Style for required checkbox validation */
        .checkbox-group.error input[type="checkbox"] {
            border-color: #dc3545;
            animation: shake 0.5s linear;
        }

        @keyframes shake {
            0% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            50% { transform: translateX(5px); }
            75% { transform: translateX(-5px); }
            100% { transform: translateX(0); }
        }

        .full-width {
            grid-column: 1 / -1;
        }

        textarea {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid var(--primary-blue);
            background-color: #f8f9fa;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            color: var(--text-dark);
            resize: vertical;
            min-height: 120px;
            font-family: inherit;
        }

        textarea:hover {
            border-color: var(--primary-pink);
        }

        textarea:focus {
            border-color: var(--primary-pink);
            outline: none;
            box-shadow: 0 0 0 3px rgba(205, 32, 139, 0.1);
            background-color: white;
        }

        .char-counter {
            font-size: 0.8rem;
            color: #666;
            text-align: right;
            margin-top: 4px;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            align-items: center;
            justify-content: center;
            overflow-y: auto;
        }

        .modal.show {
            display: flex;
        }

        .modal-content {
            background-color: #fff;
            border-radius: 8px;
            max-width: 500px; /* Reduced max-width for better presentation */
            width: 90%;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            position: relative;
            margin: auto;
            transform: translateY(0); /* Remove the transform */
            top: 0; /* Remove the top positioning */
        }

        /* Additional styles for the success message */
        .success-icon {
            font-size: 64px;
            color: #28a745;
            margin-bottom: 30px;
            animation: scaleIn 0.5s ease-in-out;
        }

        @keyframes scaleIn {
            0% { transform: scale(0); }
            60% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }

        .modal h2 {
            font-size: 28px;
            margin-bottom: 20px;
            color: var(--primary-blue);
        }

        .modal p {
            font-size: 18px;
            margin-bottom: 30px;
            line-height: 1.5;
            color: var(--text-dark);
            padding: 0 20px;
        }

        .text-center {
            text-align: center;
        }

        .photo-options {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-top: 20px;
        }

        .photo-options button {
            padding: 15px 25px;
            border: none;
            border-radius: 8px;
            background-color: var(--primary-blue);
            color: white;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
            transition: background-color 0.3s;
        }

        .photo-options button:hover {
            background-color: var(--primary-pink);
        }

        .photo-options i {
            font-size: 24px;
        }

        #cameraFeed {
            width: 100%;
            max-width: 640px;
            margin: 0 auto;
            display: block;
        }

        .camera-controls {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 15px;
        }

        .camera-controls button {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            background-color: var(--primary-blue);
            color: white;
            cursor: pointer;
        }

        .close {
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .upload-group {
            margin: 20px 0;
        }

        .upload-group label {
            display: block;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .id-upload-container {
            display: flex;
            gap: 20px;
            margin-bottom: 10px;
        }

        .id-upload-box {
            flex: 1;
            position: relative;
            height: 200px;
            border: 1px solid var(--primary-blue);
            background-color: #f8f9fa;
            border-radius: 8px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .id-upload-box:hover {
            border-color: var(--primary-pink);
            background-color: #f0f0f0;
        }

        .id-preview {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 1;
        }

        .id-preview i {
            font-size: 48px;
            color: #6c757d;
            margin-bottom: 10px;
        }

        .id-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: none;
        }

        .id-upload-box input[type="file"] {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            opacity: 0;
            cursor: pointer;
            z-index: 2;
        }

        .upload-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 8px;
            text-align: center;
            transform: translateY(100%);
            transition: transform 0.3s ease;
        }

        .id-upload-box:hover .upload-overlay {
            transform: translateY(0);
        }

        .upload-requirements {
            margin-top: 10px;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .upload-requirements small {
            color: #6c757d;
            font-size: 0.875rem;
        }

        .upload-error {
            border-color: #dc3545;
            animation: shake 0.5s linear;
        }

        @keyframes shake {
            0% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            50% { transform: translateX(5px); }
            75% { transform: translateX(-5px); }
            100% { transform: translateX(0); }
        }
        #successModal .modal-content {
        text-align: center;
        padding: 30px;
        max-width: 400px;
    }

    #successModal .fas.fa-check-circle {
        animation: scaleIn 0.5s ease-in-out;
    }

    .text-center {
        text-align: center;
    }

    #successModal button {
        margin-top: 20px;
        padding: 10px 25px;
        background-color: var(--primary-blue);
        color: white;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        transition: background-color 0.3s;
    }

    #successModal button:hover {
        background-color: var(--primary-pink);
    }

    .steps-container {
        display: flex;
        justify-content: center;
        gap: 40px;
        margin-bottom: 40px;
    }

    .step {
        display: flex;
        align-items: center;
        gap: 10px;
        opacity: 0.5;
        transition: all 0.3s ease;
    }

    .step.active {
        opacity: 1;
    }

    .step-number {
        width: 30px;
        height: 30px;
        background: var(--primary-blue);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
    }

    .step.active .step-number {
        background: var(--primary-pink);
    }

    .step-text {
        font-weight: 500;
        color: var(--text-dark);
    }

    .form-section {
        display: block;
        transition: all 0.3s ease;
    }

    .form-section.hidden {
        display: none;
    }

    .button-container {
        display: flex;
        justify-content: center;
        gap: 15px;
        margin-top: 20px;
    }

    .btn {
        padding: 0 20px;
        border-radius: 8px;
        font-size: 1rem;
        font-weight: 600;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 120px;
        height: 50px;
    }

    .btn-primary {
        background: var(--primary-pink);
        border: 2px solid var(--primary-pink);
        color: var(--text-light);
    }

    .btn-outline {
        background: white;
        border: 2px solid var(--primary-blue);
        color: var(--primary-blue);
    }

        .btn-gigs {
            background: var(--primary-blue);
            border: 2px solid var(--primary-blue);
            color: var(--text-light);
            width: 200px;
        }

        .btn-primary:hover {
            background: white;
            border: 2px solid var(--primary-pink);
            color: var(--primary-pink);
            text-decoration: none;
        }

        .btn-outline:hover {
            background: var(--primary-blue);
            color: var(--text-light);
            text-decoration: none;
        }

        .btn-gigs:hover {
            background: white;
            color: var(--primary-blue);
        }


    .mobile-input-group {
        display: flex;
        align-items: center;
        border: 1px solid var(--primary-blue);
        border-radius: 8px;
        overflow: hidden;
        background-color: #f8f9fa;
        transition: all 0.3s ease;
    }

    .mobile-input-group:hover {
        border-color: var(--primary-pink);
    }

    .mobile-input-group:focus-within {
        border-color: var(--primary-pink);
        outline: none;
        box-shadow: 0 0 0 3px rgba(205, 32, 139, 0.1);
        background-color: white;
    }

    .country-code {
        background-color: transparent;
        padding: 12px 16px;
        border-right: 1px solid var(--primary-blue);
        color: var(--text-dark);
        font-size: 1rem;
        white-space: nowrap;
    }

    .mobile-input-group input {
        border: none !important;
        border-radius: 0 !important;
        box-shadow: none !important;
        background-color: transparent !important;
    }

    .mobile-input-group.error {
        border-color: #dc3545;
    }

    .password-input-group {
        position: relative;
        display: flex;
        align-items: center;
    }

    .password-input-group input {
        width: 100%;
    }

    .password-toggle {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
        color: #6c757d;
        padding: 5px;
        z-index: 2;
    }

    .password-toggle:hover {
        color: var(--primary-blue);
    }

    .required {
        color: red;
        margin-left: 2px;
    }

        #validation-errors ul, #validation-errors-step2 ul{
            list-style-type: none;
            padding: 0;
            margin: 0;
        }
        #validation-errors li, #validation-errors-step2 li{
            color: red; 
            display: flex;
            justify-content: left;
            align-items: center;
            margin-bottom: 5px;
        }
        #validation-errors li::before, #validation-errors-step2 li::before{
            content: "•";
            margin-right: 10px;
        }
    .loading-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.9);
        z-index: 9999;
        justify-content: center;
        align-items: center;
    }

    .loading-overlay.show {
        display: flex;
    }

    .loading-spinner {
        text-align: center;
    }

    .spinner {
        width: 50px;
        height: 50px;
        border: 5px solid #f3f3f3;
        border-top: 5px solid var(--primary-blue);
        border-radius: 50%;
        margin: 0 auto 15px;
        animation: spin 1s linear infinite;
    }

    .loading-spinner p {
        color: var(--primary-blue);
        font-weight: 500;
        margin-top: 10px;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    @media screen and (max-width: 360px) {
        /* Container and Layout */
        .container {
            padding: 10px;
        }

        .form-section {
            padding: 15px;
        }

        .form-grid {
            grid-template-columns: 1fr;
            gap: 10px;
        }

        /* Header */
        .header h1 {
            font-size: 24px;
        }

        .header p {
            font-size: 16px;
        }

        /* Logo Container */
        .logo-container img {
            width: 80px;
        }

        .logo-container h1 {
            font-size: 1.5rem;
            margin-left: -1rem;
        }

        /* Profile Section */
        .profile-photo {
            width: 100px;
            height: 100px;
            margin: 0 auto;
        }

        /* Form Elements */
        .form-group {
            margin-bottom: 12px;
        }

        .form-group label {
            font-size: 14px;
            margin-bottom: 4px;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            padding: 8px;
            font-size: 14px;
            width: 100%;
        }

        /* Mobile Number Input */
        .mobile-input-group {
            display: flex;
            gap: 8px;
        }

        .country-code {
            width: 80px;
            font-size: 14px;
        }

        /* Steps Container */
        .steps-container {
            margin: 20px 0;
        }

        .step {
            padding: 8px;
        }

        .step-number {
            width: 24px;
            height: 24px;
            font-size: 12px;
        }

        .step-text {
            font-size: 12px;
        }

        /* ID Upload Section */
        .upload-group {
            margin: 15px 0;
            width: 100%;
        }

        .id-upload-container {
            flex-direction: column;
            gap: 15px;
            width: 100%;
            margin-bottom: 15px;
        }

        .id-upload-box {
            width: 100%;
            height: 150px;
            position: relative;
            margin: 0;
            border: 2px dashed var(--primary-blue);
            background-color: #f8f9fa;
            border-radius: 8px;
        }

        .id-preview {
            position: relative;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 1;
        }

        .id-upload-box input[type="file"] {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            opacity: 0;
            cursor: pointer;
            z-index: 2;
        }

        .upload-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 8px;
            text-align: center;
            z-index: 3;
        }

        /* Improve visual feedback for touch */
        .id-upload-box:active {
            background-color: #e9ecef;
            border-color: var(--primary-pink);
        }

        /* Make icons and text more visible */
        .id-preview i {
            font-size: 32px;
            color: var(--primary-blue);
            margin-bottom: 10px;
        }

        .id-preview span {
            font-size: 14px;
            color: #495057;
            text-align: center;
        }

        /* Buttons */
        .button-container {
            flex-direction: column;
            gap: 10px;
        }

        .button-container button {
            width: 100%;
            padding: 10px;
            font-size: 14px;
        }

        /* Modal */
        .modal-content {
            padding: 15px;
            width: 95%;
        }

        .success-icon {
            font-size: 48px;
        }

        .modal h2 {
            font-size: 20px;
        }

        .modal p {
            font-size: 14px;
            padding: 0 10px;
        }
    }
    </style>
</head>
<body>
    <div id="successModal" class="modal">
        <div class="modal-content">
            <div class="text-center">
                <i class="fas fa-check-circle success-icon"></i>
                <h2>Registration Successful!</h2>
                <p>Thank you for registering as a Genius. We will review your application and notify you via email once your account has been approved.</p>
                <a href="{{ url_for('landing_page') }}" class="btn btn-gigs">Return to Home</a>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="header">
            <!-- Logo at the top -->
            <div class="logo-container">
                <a href="{{ url_for('landing_page') }}">
                    <img src="{{ url_for('static', filename='img/giggenius_logo.jpg') }}" alt="GigGenius Logo">
                </a>
            </div>
            <!-- Heading below the logo -->
            <h1>Join as a Genius</h1>
            <p class="subtitle">Start your journey with GigGenius</p>
        </div>

        <!-- Progress Steps -->
        <div class="steps-container">
            <div class="step active" id="step1">
                <span class="step-number">1</span>
                <span class="step-text">Personal Info</span>
            </div>
            <div class="step" id="step2">
                <span class="step-number">2</span>
                <span class="step-text">Professional Info</span>
            </div>
        </div>

        <form id="registrationForm" method="POST" enctype="multipart/form-data">
            <!-- Step 1: Personal Information -->
            <div class="form-section" id="section1">
                <div class="profile-section">
                    <div class="profile-photo" onclick="document.getElementById('profilePhoto').click()">
                        <span id="photoPrompt">+ Add Profile Photo</span>
                        <input type="file" id="profilePhoto" name="profilePhoto" accept="image/*" hidden>
                        <img id="photoPreview" style="width: 100%; height: 100%; object-fit: cover; display: none;">
                    </div>
                </div>

                <div class="form-grid">
                    <div class="form-group">
                        <label for="firstName">First Name <span class="required">*</span></label>
                        <input type="text" id="firstName" name="firstName" required>
                    </div>

                    <div class="form-group">
                        <label for="lastName">Last Name <span class="required">*</span></label>
                        <input type="text" id="lastName" name="lastName" required>
                    </div>

                    <div class="form-group">
                        <label for="email">Email Address <span class="required">*</span></label>
                        <input type="email" id="email" name="email" class="form-control" required>
                        <div id="email-error" class="error-message" style="display: none;"></div>
                    </div>

                    <div class="form-group">
                        <label for="password">Password <span class="required">*</span></label>
                        <div class="password-input-group">
                            <input type="password" id="password" name="password" required>
                            <i class="fas fa-eye-slash password-toggle" id="togglePassword"></i>
                        </div>
                        <div class="error-message" id="password-error" style="display: none;"></div>
                    </div>

                    <div class="form-group">
                        <label for="birthday">Birthday <span class="required">*</span></label>
                        <input type="date" id="birthday" name="birthday" required>
                    </div>

                    <div class="form-group">
                        <label for="country">Country <span class="required">*</span></label>
                        <select id="country" name="country">
                            <option value="">Select your country</option>
                            <option value="Philippines">Philippines</option>
                            <option value="United States">United States</option>
                            <option value="United Kingdom">United Kingdom</option>
                            <option value="Canada">Canada</option>
                            <option value="Australia">Australia</option>
                            <option value="Singapore">Singapore</option>
                            <option value="Japan">Japan</option>
                            <option value="South Korea">South Korea</option>
                            <option value="India">India</option>
                            <option value="Malaysia">Malaysia</option>
                            <option value="Indonesia">Indonesia</option>
                            <option value="Thailand">Thailand</option>
                            <option value="Vietnam">Vietnam</option>
                            <option value="New Zealand">New Zealand</option>
                            <option value="Germany">Germany</option>
                            <option value="France">France</option>
                            <option value="Italy">Italy</option>
                            <option value="Spain">Spain</option>
                            <option value="Netherlands">Netherlands</option>
                            <option value="Sweden">Sweden</option>
                            <option value="Norway">Norway</option>
                            <option value="Denmark">Denmark</option>
                            <option value="Finland">Finland</option>
                            <option value="Ireland">Ireland</option>
                            <option value="Switzerland">Switzerland</option>
                            <option value="Belgium">Belgium</option>
                            <option value="Austria">Austria</option>
                            <option value="Portugal">Portugal</option>
                            <option value="Greece">Greece</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="mobile">Mobile Number <span class="required">*</span></label>
                        <div class="mobile-input-group">
                            <span class="country-code" id="selectedCountryCode">+63</span>
                            <input type="tel" id="mobile" name="mobile" pattern="[0-9]*" inputmode="numeric">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="position">Job Title<span class="required">*</span></label>
                        <input type="text" id="position" name="position" required>
                    </div>
                </div>

                <!-- Error messages container -->
                <div id="validation-errors" class="error-container" style="display: none;"></div>

                <!-- Button container with back and next buttons -->
                <div class="button-container">
                    <button type="button" onclick="goBack()" class="btn btn-outline">Back</button>
                    <button type="button" onclick="validateStep1()" class="btn btn-primary">Next</button>
                </div>
            </div>

            <!-- Step 2: Professional Information -->
            <div class="form-section hidden" id="section2">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="expertise">Expertise Level <span class="required">*</span></label>
                        <select id="expertise" name="expertise" novalidate>
                            <option value="">Select Level</option>
                            <option value="Entry">Entry Level</option>
                            <option value="Intermediate">Intermediate</option>
                            <option value="Expert">Expert</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="rate">Hourly Rate (USD) <span class="required">*</span></label>
                        <input type="number" id="rate" name="hourly_rate" min="1" novalidate>
                    </div>

                    <div class="form-group">
                        <label for="availability">Availability <span class="required">*</span></label>
                        <select id="availability" name="availability" novalidate>
                            <option value="">Select availability</option>
                            <option value="fulltime">Full Time (40hrs/week)</option>
                            <option value="parttime">Part Time (20hrs/week)</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="taxId">Tax Identification Number <span class="required">*</span></label>
                        <input type="text" id="taxId" name="tax_id" novalidate>
                    </div>

                    <div class="form-group full-width">
                        <label for="introduction">Introduction <span class="required">*</span></label>
                        <textarea 
                            id="introduction" 
                            name="introduction" 
                            maxlength="300" 
                            required
                            placeholder="Brief introduction about yourself and your expertise"
                        ></textarea>
                        <div id="introCounter" class="char-counter"></div>
                    </div>

                    <div class="form-group full-width">
                        <label for="professionalSummary">Professional Summary <span class="required">*</span></label>
                        <textarea 
                            id="professionalSummary" 
                            name="professional_sum" 
                            maxlength="3000" 
                            required
                            placeholder="Detailed summary of your professional background, skills, and achievements"
                        ></textarea>
                        <div id="summaryCounter" class="char-counter"></div>
                    </div>

                    <div class="upload-group full-width">
                        <label>ID Verification <span class="required">*</span></label>
                        <div class="id-upload-container">
                            <div class="id-upload-box">
                                <div class="id-preview" id="idFrontPreview">
                                    <i class="fas fa-id-card"></i>
                                    <span>Front of ID</span>
                                </div>
                                <input type="file" id="idFront" name="idFront" accept="image/*">
                                <div class="upload-overlay">
                                    <span>Click to Upload</span>
                                </div>
                            </div>
                            <div class="id-upload-box">
                                <div class="id-preview" id="idBackPreview">
                                    <i class="fas fa-id-card"></i>
                                    <span>Back of ID</span>
                                </div>
                                <input type="file" id="idBack" name="idBack" accept="image/*">
                                <div class="upload-overlay">
                                    <span>Click to Upload</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="checkbox-group full-width">
                        <input type="checkbox" id="emailSubscription" name="email_updates">
                        <label for="emailSubscription">
                            I want to receive email updates about <span style="color: var(--primary-pink);">GigGenius</span> services and latest opportunities.
                        </label>
                    </div>
                    
                    <div class="checkbox-group full-width">
                        <input type="checkbox" id="termsAgreement" name="terms_agreement">
                        <label for="termsAgreement">
                            I understand and accept the GigGenius <a href="{{ url_for('terms_of_service') }}">Terms of Service</a>, <a href="{{ url_for('user_agreement') }}">User Agreement,</a> and <a href="{{ url_for('privacy_policy') }}">Privacy Policy.</a>
                        </label>
                    </div>
                </div>

                <!-- Error messages container for step 2 -->
                <div id="validation-errors-step2" class="error-container" style="display: none;"></div>

                <div class="button-container">
                    <button type="button" onclick="previousStep()" class="btn btn-outline">Previous</button>
                    <button type="button" onclick="handleFormSubmission()" class="btn btn-primary">Submit</button>
                </div>
            </div>
        </form>
    </div>

    <script>
        // Global variables
        const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB in bytes
        let isSubmitting = false;
        
        // Country codes mapping
        const countryCodes = {
            'Philippines': '+63',
            'United States': '+1',
            'United Kingdom': '+44',
            'Canada': '+1',
            'Australia': '+61',
            'Singapore': '+65',
            'Japan': '+81',
            'South Korea': '+82',
            'India': '+91',
            'Malaysia': '+60',
            'Indonesia': '+62',
            'Thailand': '+66',
            'Vietnam': '+84',
            'New Zealand': '+64',
            'Germany': '+49',
            'France': '+33',
            'Italy': '+39',
            'Spain': '+34',
            'Netherlands': '+31',
            'Sweden': '+46',
            'Norway': '+47',
            'Denmark': '+45',
            'Finland': '+358',
            'Ireland': '+353',
            'Switzerland': '+41',
            'Belgium': '+32',
            'Austria': '+43',
            'Portugal': '+351',
            'Greece': '+30'
        };

        // DOM Content Loaded Event
        document.addEventListener('DOMContentLoaded', function() {
            initializeForm();
        });

        function initializeForm() {
            // Set initial country code (Philippines as default)
            document.getElementById('selectedCountryCode').textContent = countryCodes['Philippines'];

            // Set max date to today for birthday field
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('birthday').setAttribute('max', today);

            // Initialize event listeners
            setupEventListeners();
        }

        function setupEventListeners() {
            // Country code handling
            document.getElementById('country').addEventListener('change', function(e) {
                const country = e.target.value;
                document.getElementById('selectedCountryCode').textContent = countryCodes[country] || '+63';
            });

            // Profile photo upload
            document.getElementById('profilePhoto').addEventListener('change', handleProfilePhotoUpload);

            // ID photo uploads
            document.getElementById('idFront').addEventListener('change', handleIdFrontUpload);
            document.getElementById('idBack').addEventListener('change', handleIdBackUpload);

            // Password toggle
            const togglePassword = document.getElementById('togglePassword');
            const passwordInput = document.getElementById('password');
            togglePassword.addEventListener('click', function() {
                const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordInput.setAttribute('type', type);
                this.classList.toggle('fa-eye');
                this.classList.toggle('fa-eye-slash');
            });

            // Character counters
            createCharCounter('introduction', 300, 'introCounter');
            createCharCounter('professionalSummary', 3000, 'summaryCounter');

            // Clear error when typing
            document.querySelectorAll('input, select, textarea').forEach(input => {
                input.addEventListener('input', function() {
                    this.classList.remove('input-error');
                    const errorId = `${this.id}-error`;
                    const errorElement = document.getElementById(errorId);
                    if (errorElement) errorElement.style.display = 'none';
                });
            });

            // Email validation with immediate feedback
            const emailInput = document.getElementById('email');
            emailInput.addEventListener('input', validateEmailInput);
            emailInput.addEventListener('keyup', validateEmailInput);

            // Password validation
            document.getElementById('password').addEventListener('input', validatePasswordInput);
            document.getElementById('password').addEventListener('keyup', validatePasswordInput);

            // Mobile number validation
            document.getElementById('mobile').addEventListener('input', validateMobileInput);

            // Tax ID validation
            document.getElementById('taxId').addEventListener('input', validateTaxIdInput);

            // Birthday validation
            document.getElementById('birthday').addEventListener('input', validateBirthdayInput);

            // Submit button
            document.getElementById('submitButton').addEventListener('click', handleFormSubmission);

            // Prevent default form submission
            document.getElementById('registrationForm').addEventListener('submit', function(e) {
                e.preventDefault();
            });
        }

        // Photo handling functions
        function handleProfilePhotoUpload(e) {
            const file = e.target.files[0];
            if (!file) return;

            // Validate file type and size
            if (!file.type.startsWith('image/')) {
                displayValidationError('Please select an image file (JPEG, PNG)');
                e.target.value = '';
                return;
            }

            if (file.size > MAX_FILE_SIZE) {
                displayValidationError('Profile photo must be less than 5MB');
                e.target.value = '';
                return;
            }

            // Preview the image
            const reader = new FileReader();
            reader.onload = function(e) {
                const preview = document.getElementById('photoPreview');
                const photoPrompt = document.getElementById('photoPrompt');
                
                preview.src = e.target.result;
                preview.style.display = 'block';
                photoPrompt.style.display = 'none';
            };
            reader.readAsDataURL(file);
        }

        function handleIdFrontUpload(e) {
            const file = e.target.files[0];
            if (!file) return;

            // Validate file type and size
            if (!file.type.startsWith('image/')) {
                displayValidationError('Please select an image file (JPEG, PNG) for ID front');
                e.target.value = '';
                return;
            }

            if (file.size > MAX_FILE_SIZE) {
                displayValidationError('ID front photo must be less than 5MB');
                e.target.value = '';
                return;
            }

            // Preview the image
            const reader = new FileReader();
            reader.onload = function(e) {
                const preview = document.getElementById('idFrontPreview');
                const idPrompt = preview.querySelector('span');
                const idIcon = preview.querySelector('i');
                
                const previewImg = preview.querySelector('img') || document.createElement('img');
                previewImg.src = e.target.result;
                previewImg.style.width = '100%';
                previewImg.style.height = '100%';
                previewImg.style.objectFit = 'cover';
                previewImg.style.display = 'block';
                preview.appendChild(previewImg);
                
                if (idPrompt) idPrompt.style.display = 'none';
                if (idIcon) idIcon.style.display = 'none';
            };
            reader.readAsDataURL(file);
        }

        function handleIdBackUpload(e) {
            const file = e.target.files[0];
            if (!file) return;

            // Validate file type and size
            if (!file.type.startsWith('image/')) {
                displayValidationError('Please select an image file (JPEG, PNG) for ID back');
                e.target.value = '';
                return;
            }

            if (file.size > MAX_FILE_SIZE) {
                displayValidationError('ID back photo must be less than 5MB');
                e.target.value = '';
                return;
            }

            // Preview the image
            const reader = new FileReader();
            reader.onload = function(e) {
                const preview = document.getElementById('idBackPreview');
                const idPrompt = preview.querySelector('span');
                const idIcon = preview.querySelector('i');
                
                const previewImg = preview.querySelector('img') || document.createElement('img');
                previewImg.src = e.target.result;
                previewImg.style.width = '100%';
                previewImg.style.height = '100%';
                previewImg.style.objectFit = 'cover';
                previewImg.style.display = 'block';
                preview.appendChild(previewImg);
                
                if (idPrompt) idPrompt.style.display = 'none';
                if (idIcon) idIcon.style.display = 'none';
            };
            reader.readAsDataURL(file);
        }

        // Input validation functions
        function validateEmailInput() {
            const email = this.value.trim();
            const errorElement = document.getElementById('email-error');
            
            // If empty, hide error
            if (email === '') {
                errorElement.style.display = 'none';
                this.classList.remove('input-error');
                return false;
            }

            // Show error message immediately if there's no @ symbol
            if (!email.includes('@')) {
                errorElement.style.display = 'block';
                errorElement.textContent = 'Please enter a valid email address (e.g., <EMAIL>)';
                this.classList.add('input-error');
                return false;
            }

            // More detailed validation if @ is present
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                errorElement.style.display = 'block';
                errorElement.textContent = 'Please enter a valid email address (e.g., <EMAIL>)';
                this.classList.add('input-error');
                return false;
            }

            // Check email uniqueness immediately
            checkEmailUnique(email).then(isUnique => {
                if (!isUnique) {
                    errorElement.style.display = 'block';
                    errorElement.textContent = 'This email is already registered';
                    this.classList.add('input-error');
                    return false;
                } else {
                    errorElement.style.display = 'none';
                    this.classList.remove('input-error');
                    return true;
                }
            });

            return true;
        }

        function validatePasswordInput() {
            const password = this.value;
            const errorElement = document.getElementById('password-error');
            
            // If empty, hide error
            if (password.trim() === '') {
                errorElement.style.display = 'none';
                this.classList.remove('input-error');
                return false;
            }

            const minLength = password.length >= 8;
            const hasUpperCase = /[A-Z]/.test(password);
            const hasLowerCase = /[a-z]/.test(password);
            const hasNumber = /[0-9]/.test(password);
            const hasSpecial = /[!@#$%^&*]/.test(password);

            // Show specific error message based on what's missing
            if (!minLength || !hasUpperCase || !hasLowerCase || !hasNumber || !hasSpecial) {
                errorElement.style.display = 'block';
                this.classList.add('input-error');
                
                let errorMessage = 'Password must:';
                if (!minLength) errorMessage += '<br>• Be at least 8 characters';
                if (!hasUpperCase) errorMessage += '<br>• Include an uppercase letter (A-Z)';
                if (!hasLowerCase) errorMessage += '<br>• Include a lowercase letter (a-z)';
                if (!hasNumber) errorMessage += '<br>• Include a number (0-9)';
                if (!hasSpecial) errorMessage += '<br>• Include a special character (!@#$%^&*)';
                
                errorElement.innerHTML = errorMessage;
                return false;
            }

            errorElement.style.display = 'none';
            this.classList.remove('input-error');
            return true;
        }

        function validateMobileInput() {
            // Remove any non-numeric characters
            this.value = this.value.replace(/\D/g, '');
            
            const mobile = this.value;
            const errorElement = document.getElementById('mobile-error');
            
            if (mobile.trim() === '') {
                errorElement.style.display = 'none';
                return false;
            }

            errorElement.style.display = 'none';
            return true;
        }

        function validateTaxIdInput() {
            const taxId = this.value;
            // Remove any non-numeric characters immediately
            this.value = this.value.replace(/\D/g, '');
            
            const errorElement = document.getElementById('taxId-error');
            
            if (taxId.trim() === '') {
                errorElement.style.display = 'none';
                this.classList.remove('input-error');
                return false;
            }
            
            const isValid = /^\d+$/.test(taxId.trim());
            if (!isValid) {
                errorElement.style.display = 'block';
                this.classList.add('input-error');
                return false;
            }
            
            errorElement.style.display = 'none';
            this.classList.remove('input-error');
            return true;
        }

        function validateBirthdayInput() {
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            
            const selectedDate = new Date(this.value);
            selectedDate.setHours(0, 0, 0, 0);
            
            // Get or create error element
            let errorElement = document.getElementById('birthday-error');
            if (!errorElement) {
                errorElement = document.createElement('div');
                errorElement.id = 'birthday-error';
                errorElement.className = 'error-message';
                this.parentNode.insertBefore(errorElement, this.nextSibling);
            }

            // Clear any existing error
            errorElement.textContent = '';
            this.classList.remove('input-error');

            // Check if date is empty
            if (!this.value.trim()) {
                errorElement.style.display = 'none';
                return false;
            }

            // Check if date is in the future
            if (selectedDate > today) {
                errorElement.textContent = 'Birthday cannot be a future date';
                errorElement.style.display = 'block';
                this.classList.add('input-error');
                return false;
            }

            // Extract date components
            const [year, month, day] = this.value.split('-').map(Number);
            const currentYear = today.getFullYear();

            // Validate year
            if (year > currentYear) {
                errorElement.textContent = `Year cannot exceed ${currentYear}`;
                errorElement.style.display = 'block';
                this.classList.add('input-error');
                return false;
            }

            // Validate month
            if (month < 1 || month > 12) {
                errorElement.textContent = 'Month must be between 01-12';
                errorElement.style.display = 'block';
                this.classList.add('input-error');
                return false;
            }

            // Validate day
            const daysInMonth = new Date(year, month, 0).getDate();
            if (day < 1 || day > daysInMonth) {
                errorElement.textContent = `Day must be between 01-${daysInMonth.toString().padStart(2, '0')}`;
                errorElement.style.display = 'block';
                this.classList.add('input-error');
                return false;
            }

            // If all validations pass
            errorElement.style.display = 'none';
            return true;
        }

        // Helper functions
        function createCharCounter(textareaId, maxLength, counterId) {
            const textarea = document.getElementById(textareaId);
            const counter = document.getElementById(counterId);

            function updateCharCount() {
                const remaining = maxLength - textarea.value.length;
                counter.textContent = `${remaining} characters remaining`;
                counter.style.color = remaining < maxLength * 0.1 ? '#dc2626' : '#666';
            }

            textarea.addEventListener('input', function() {
                if (this.value.length > maxLength) {
                    this.value = this.value.substring(0, maxLength);
                }
                updateCharCount();
            });

            updateCharCount();
        }

        function displayFieldError(field, message) {
            const errorElement = document.createElement('div');
            errorElement.className = 'error-message';
            errorElement.textContent = message;
            
            // Insert after the field
            field.parentNode.insertBefore(errorElement, field.nextSibling);
            field.classList.add('input-error');
        }

        function displayValidationError(message) {
            const errorContainer = document.getElementById('validation-errors') || 
                                 document.getElementById('validation-errors-step2');
            if (errorContainer) {
                errorContainer.innerHTML = `<strong>Error:</strong> ${message}`;
                errorContainer.style.display = 'block';
                errorContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }

        function displayValidationErrors(errors, containerId) {
            const errorContainer = document.getElementById(containerId);
            if (errorContainer) {
                errorContainer.innerHTML = `
                    <ul class="error-list">
                        ${errors.map(error => `<li>${error}</li>`).join('')}
                    </ul>
                `;
                errorContainer.style.display = 'block';
                errorContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }

        function clearErrors() {
            document.querySelectorAll('.input-error').forEach(el => el.classList.remove('input-error'));
            document.querySelectorAll('.error-container').forEach(el => {
                el.style.display = 'none';
                el.innerHTML = '';
            });
            document.querySelectorAll('.error-message').forEach(el => {
                if (el.id !== 'email-error' && el.id !== 'password-error' && 
                    el.id !== 'mobile-error' && el.id !== 'taxId-error') {
                    el.remove();
                }
            });
        }

        // Form navigation
        function nextStep() {
            document.getElementById('section1').classList.add('hidden');
            document.getElementById('section2').classList.remove('hidden');
            document.getElementById('step1').classList.remove('active');
            document.getElementById('step2').classList.add('active');
        }

        function previousStep() {
            document.getElementById('section2').classList.add('hidden');
            document.getElementById('section1').classList.remove('hidden');
            document.getElementById('step2').classList.remove('active');
            document.getElementById('step1').classList.add('active');
        }

        function goBack() {
            if (document.referrer) {
                window.history.back();
            } else {
                window.location.href = "{{ url_for('landing_page') }}";
            }
        }

        async function validateStep1() {
            const errors = [];
            clearErrors();

            // Profile photo validation
            const profilePhoto = document.getElementById('profilePhoto');
            if (!profilePhoto.files || profilePhoto.files.length === 0) {
                errors.push('Profile photo is required');
            } else if (profilePhoto.files[0].size > MAX_FILE_SIZE) {
                errors.push('Profile photo must be less than 5MB');
            }

            // Required fields validation
            const requiredFields = [
                { id: 'firstName', label: 'First Name' },
                { id: 'lastName', label: 'Last Name' },
                { id: 'email', label: 'Email' },
                { id: 'password', label: 'Password' },
                { id: 'birthday', label: 'Birthday' },
                { id: 'country', label: 'Country' },
                { id: 'mobile', label: 'Mobile Number' },
                { id: 'position', label: 'Job Title' }
            ];

            // Check required fields
            requiredFields.forEach(field => {
                const element = document.getElementById(field.id);
                if (!element.value.trim()) {
                    errors.push(`${field.label} is required`);
                    element.classList.add('input-error');
                }
            });

            // Validate birthday specifically
            const birthdayInput = document.getElementById('birthday');
            if (birthdayInput.value.trim()) {
                const today = new Date();
                today.setHours(0, 0, 0, 0);
                const selectedDate = new Date(birthdayInput.value);
                selectedDate.setHours(0, 0, 0, 0);

                if (selectedDate > today) {
                    errors.push('Please enter a valid birthday');
                    birthdayInput.classList.add('input-error');
                    const errorElement = document.getElementById('birthday-error');
                    if (errorElement) {
                        errorElement.textContent = 'Please enter a valid birthday';;
                        errorElement.style.display = 'block';
                    }
                }
            }

            // Validate email format
            const email = document.getElementById('email').value.trim();
            if (email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(email)) {
                    errors.push('Please enter a valid email address');
                }
            }

            // Validate password
            const password = document.getElementById('password').value.trim();
            if (password && !validatePasswordInput.call(document.getElementById('password'))) {
                errors.push('Password does not meet requirements');
            }

            // If there are any errors, display them and return false
            if (errors.length > 0) {
                displayValidationErrors(errors, 'validation-errors');
                return false;
            }

            // If all validations pass, proceed to next step
            nextStep();
            return true;
        }

        async function checkEmailUnique(email) {
            try {
                const response = await fetch('/check_email', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email: email })
                });
                
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                
                const data = await response.json();
                return !data.exists; // Returns true if email is unique
            } catch (error) {
                console.error('Error checking email:', error);
                return false; // Assume email is not unique if there's an error
            }
        }

        function validateStep2() {
            const errors = [];
            clearErrors();

            // Check required fields
            const requiredFields = [
                { id: 'expertise', label: 'Expertise Level' },
                { id: 'rate', label: 'Hourly Rate' },
                { id: 'availability', label: 'Availability' },
                { id: 'taxId', label: 'Tax Identification Number' },
                { id: 'introduction', label: 'Introduction' },
                { id: 'professionalSummary', label: 'Professional Summary' }
            ];

            // Validate required fields
            requiredFields.forEach(field => {
                const element = document.getElementById(field.id);
                if (!element.value.trim()) {
                    errors.push(`${field.label} is required`);
                    element.classList.add('input-error');
                }
            });

            // Special validation for tax ID (numbers only)
            const taxId = document.getElementById('taxId').value;
            if (taxId.trim() && !/^\d+$/.test(taxId.trim())) {
                errors.push('Tax ID must contain numbers only');
                document.getElementById('taxId').classList.add('input-error');
            }

            // ID verification validation
            const idFront = document.getElementById('idFront');
            const idBack = document.getElementById('idBack');
            
            if (!idFront.files || idFront.files.length === 0) {
                errors.push('Front ID photo is required');
            } else if (idFront.files[0].size > MAX_FILE_SIZE) {
                errors.push('Front ID photo must be less than 5MB');
            }
            
            if (!idBack.files || idBack.files.length === 0) {
                errors.push('Back ID photo is required');
            } else if (idBack.files[0].size > MAX_FILE_SIZE) {
                errors.push('Back ID photo must be less than 5MB');
            }
            
            // Terms agreement validation
            const termsAgreement = document.getElementById('termsAgreement');
            if (!termsAgreement.checked) {
                errors.push('You must accept the Terms and Conditions');
                termsAgreement.classList.add('input-error');
            }

            if (errors.length > 0) {
                displayValidationErrors(errors, 'validation-errors-step2');
                return false;
            }

            return true;
        }

        // Form submission
        async function handleFormSubmission() {
            if (isSubmitting) return;

            // Validate step 2 first
            if (!validateStep2()) {
                return;
            }

            isSubmitting = true;
            showLoadingOverlay();

            try {
                const formData = new FormData(document.getElementById('registrationForm'));
                
                // Set checkbox values properly
                formData.set('email_updates', document.getElementById('emailSubscription').checked ? '1' : '0');
                formData.set('terms_agreement', document.getElementById('termsAgreement').checked ? '1' : '0');

                const response = await fetch('/register_genius', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    hideLoadingOverlay();
                    document.getElementById('successModal').classList.add('show');
                } else {
                    throw new Error(data.error || 'Registration failed');
                }
            } catch (error) {
                console.error('Error:', error);
                hideLoadingOverlay();
                displayValidationError(error.message.includes('too large') ? 
                    'File upload failed: Image size must be less than 5MB' : 
                    'Registration failed: ' + error.message
                );
            } finally {
                isSubmitting = false;
            }
        }

        function showLoadingOverlay() {
            const overlay = document.createElement('div');
            overlay.id = 'loadingOverlay';
            overlay.className = 'loading-overlay show';
            overlay.innerHTML = `
                <div class="loading-spinner">
                    <div class="spinner"></div>
                    <p>Processing your registration...</p>
                </div>
            `;
            document.body.appendChild(overlay);
        }

        function hideLoadingOverlay() {
            const overlay = document.getElementById('loadingOverlay');
            if (overlay) {
                overlay.remove();
            }
        }
    </script>
</body>
</html>
