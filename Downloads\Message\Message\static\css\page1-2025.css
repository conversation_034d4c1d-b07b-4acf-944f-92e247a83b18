:root {
    /* Primary colors */
    --primary-blue: #004AAD;
    --primary-pink: #CD208B;
    --primary-light-blue: #E6F0FF;
    --primary-light-pink: #FFF0F8;

    /* Neutral colors */
    --neutral-100: #FFFFFF;
    --neutral-200: #F8FAFC;
    --neutral-300: #EEF2F6;
    --neutral-400: #E2E8F0;
    --neutral-500: #CBD5E1;
    --neutral-600: #94A3B8;
    --neutral-700: #64748B;
    --neutral-800: #334155;
    --neutral-900: #1E293B;

    /* Accent colors */
    --success: #10B981;
    --warning: #F59E0B;
    --error: #EF4444;
    --info: #3B82F6;

    /* Typography */
    --font-family: 'Poppins', system-ui, -apple-system, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-md: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;

    /* Spacing */
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-10: 2.5rem;
    --spacing-12: 3rem;
    --spacing-16: 4rem;
    --spacing-20: 5rem;

    /* Borders */
    --border-radius-sm: 0.25rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    --border-radius-full: 9999px;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
    --shadow-blue: 0 8px 16px rgba(0, 74, 173, 0.15);
    --shadow-pink: 0 8px 16px rgba(205, 32, 139, 0.15);

    /* Transitions */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background: linear-gradient(135deg, var(--neutral-200) 0%, #f1f5f9 100%);
    color: var(--neutral-900);
    line-height: 1.6;
    min-height: 100vh;
    padding: 0;
    margin: 0;
    overflow-x: hidden;
}

/* Page wrapper - Full Screen */
.page-wrapper {
    min-height: 100vh;
    width: 100%;
    background: linear-gradient(135deg, var(--neutral-100) 0%, var(--neutral-200) 100%);
}

/* Container - Full Screen */
.container {
    min-height: 100vh;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--spacing-8) var(--spacing-10);
    background-color: var(--neutral-100);
    display: flex;
    flex-direction: column;
    position: relative;
}

/* Header Section */
.header-section {
    margin-bottom: var(--spacing-8);
}

/* Title section */
.title-section {
    margin: var(--spacing-8) 0 var(--spacing-6);
    text-align: center;
}

.title-section h1 {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    color: var(--primary-blue);
    margin-bottom: var(--spacing-4);
    line-height: 1.2;
    background: linear-gradient(90deg, var(--primary-blue), var(--primary-pink));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
}

.title-section p {
    font-size: var(--font-size-lg);
    color: var(--neutral-700);
    max-width: 700px;
    margin: 0 auto;
}

/* Quick Stats */
.quick-stats {
    display: flex;
    justify-content: center;
    gap: var(--spacing-8);
    margin-top: var(--spacing-6);
    padding: var(--spacing-4);
    background: linear-gradient(135deg, var(--primary-light-blue), var(--primary-light-pink));
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--neutral-300);
}

.stat-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    color: var(--neutral-700);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.stat-item i {
    color: var(--primary-blue);
    font-size: var(--font-size-md);
}

/* Main Content */
.main-content {
    flex: 1;
    max-width: 900px;
    margin: 0 auto;
    width: 100%;
}

/* Options Section */
.options-section {
    margin-bottom: var(--spacing-8);
}

.section-title {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    color: var(--neutral-800);
    margin-bottom: var(--spacing-6);
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    text-align: center;
    justify-content: center;
}

.section-title i {
    color: var(--primary-blue);
}

/* Primary Option Cards */
.primary-option-card {
    background-color: var(--neutral-100);
    border: 2px solid var(--neutral-300);
    border-radius: var(--border-radius-lg);
    margin-bottom: var(--spacing-4);
    transition: var(--transition-normal);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.primary-option-card:hover {
    border-color: var(--primary-blue);
    box-shadow: var(--shadow-blue);
    transform: translateY(-2px);
}

.primary-option-card.expanded {
    border-color: var(--primary-blue);
    box-shadow: var(--shadow-lg);
}

.option-header {
    display: flex;
    align-items: center;
    padding: var(--spacing-6);
    cursor: pointer;
    transition: var(--transition-normal);
}

.option-header:hover {
    background-color: var(--primary-light-blue);
}

.option-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius-full);
    background: linear-gradient(135deg, var(--primary-blue), var(--primary-pink));
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: var(--spacing-4);
    box-shadow: var(--shadow-md);
}

.option-icon i {
    font-size: var(--font-size-xl);
    color: var(--neutral-100);
}

.option-content {
    flex: 1;
}

.option-content h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--neutral-900);
    margin-bottom: var(--spacing-1);
}

.option-content p {
    font-size: var(--font-size-md);
    color: var(--neutral-600);
    margin: 0;
}

.option-toggle {
    margin-left: var(--spacing-4);
}

.option-toggle i {
    font-size: var(--font-size-lg);
    color: var(--primary-blue);
    transition: var(--transition-normal);
}

.option-details {
    display: none;
    padding: 0 var(--spacing-6) var(--spacing-6);
    opacity: 0;
    transform: translateY(-10px);
    transition: var(--transition-normal);
}

/* Job Type Selection */
.job-type-selection {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-4);
    margin-top: var(--spacing-4);
}

.job-type-card {
    background-color: var(--neutral-100);
    border: 2px solid var(--neutral-300);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-5);
    cursor: pointer;
    transition: var(--transition-normal);
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-4);
}

.job-type-card:hover {
    border-color: var(--primary-blue);
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.job-type-card.selected {
    border-color: var(--primary-blue);
    background-color: var(--primary-light-blue);
    box-shadow: var(--shadow-blue);
}

.job-type-icon {
    width: 50px;
    height: 50px;
    border-radius: var(--border-radius-lg);
    background: linear-gradient(135deg, var(--primary-blue), var(--primary-pink));
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.job-type-icon i {
    font-size: var(--font-size-lg);
    color: var(--neutral-100);
}

.job-type-info h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--neutral-900);
    margin-bottom: var(--spacing-2);
}

.job-type-info p {
    font-size: var(--font-size-sm);
    color: var(--neutral-600);
    margin-bottom: var(--spacing-3);
    line-height: 1.5;
}

.job-type-features {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
}

.job-type-features span {
    font-size: var(--font-size-xs);
    color: var(--neutral-700);
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.job-type-features i {
    color: var(--primary-blue);
    font-size: var(--font-size-xs);
}

/* Draft List */
.draft-list {
    margin-top: var(--spacing-4);
}

.draft-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-4);
    border: 1px solid var(--neutral-300);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-3);
    cursor: pointer;
    transition: var(--transition-normal);
    background-color: var(--neutral-100);
}

.draft-item:hover {
    border-color: var(--primary-blue);
    box-shadow: var(--shadow-sm);
    transform: translateX(4px);
}

.draft-item.selected {
    border-color: var(--primary-blue);
    background-color: var(--primary-light-blue);
    box-shadow: var(--shadow-blue);
}

.draft-info h4 {
    font-size: var(--font-size-md);
    font-weight: 600;
    color: var(--neutral-900);
    margin-bottom: var(--spacing-1);
}

.draft-date {
    font-size: var(--font-size-sm);
    color: var(--neutral-600);
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    margin: 0;
}

.draft-action {
    color: var(--primary-blue);
    font-size: var(--font-size-lg);
}

/* Rework List */
.rework-list {
    margin-top: var(--spacing-4);
}

.rework-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-5);
    border: 2px solid var(--neutral-300);
    border-radius: var(--border-radius-lg);
    margin-bottom: var(--spacing-4);
    cursor: pointer;
    transition: var(--transition-normal);
    background-color: var(--neutral-100);
}

.rework-item:hover {
    border-color: var(--primary-blue);
    box-shadow: var(--shadow-md);
    transform: translateX(4px);
}

.rework-item.selected {
    border-color: var(--primary-blue);
    background-color: var(--primary-light-blue);
    box-shadow: var(--shadow-blue);
}

.rework-info {
    flex: 1;
}

.rework-info h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--neutral-900);
    margin-bottom: var(--spacing-2);
}

.rework-status {
    font-size: var(--font-size-sm);
    color: var(--neutral-600);
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    margin-bottom: var(--spacing-2);
}

.rework-status i {
    font-size: var(--font-size-sm);
}

.rework-status i.fa-pause-circle {
    color: var(--warning);
}

.rework-status i.fa-check-circle {
    color: var(--success);
}

.rework-description {
    font-size: var(--font-size-sm);
    color: var(--neutral-700);
    margin: 0;
    line-height: 1.4;
}

.rework-action {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
}

.rework-badge {
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.rework-badge.paused {
    background-color: var(--warning);
    color: #92400e;
}

.rework-badge.completed {
    background-color: var(--success);
    color: #065f46;
}

.rework-action i {
    color: var(--primary-blue);
    font-size: var(--font-size-lg);
}

.no-rework-jobs {
    text-align: center;
    padding: var(--spacing-8);
    color: var(--neutral-600);
}

.no-rework-jobs i {
    font-size: var(--font-size-3xl);
    color: var(--neutral-400);
    margin-bottom: var(--spacing-4);
}

.no-rework-jobs p {
    font-size: var(--font-size-lg);
    font-weight: 500;
    margin-bottom: var(--spacing-2);
}

.no-rework-jobs small {
    font-size: var(--font-size-sm);
    color: var(--neutral-500);
}

/* Dropdown options */
.dropdown-options {
    display: none;
    opacity: 0;
    transform: translateY(-10px);
    transition: var(--transition-normal);
}

.dropdown-options.show {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-6);
    opacity: 1;
    transform: translateY(0);
    margin-bottom: var(--spacing-8);
}

/* Option box */
.option-box {
    padding: var(--spacing-8);
    border-radius: var(--border-radius-lg);
    background-color: var(--neutral-100);
    border: 1px solid var(--neutral-400);
    transition: var(--transition-normal);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.option-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-blue), var(--primary-pink));
    opacity: 0;
    transition: var(--transition-normal);
}

.option-box:hover::before,
.option-box.selected::before {
    opacity: 1;
}

.option-box:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--neutral-500);
}

.option-box.selected {
    background-color: var(--primary-light-blue);
    border-color: var(--primary-blue);
    box-shadow: var(--shadow-blue);
}

.option-box .icon {
    font-size: 2rem;
    margin-bottom: var(--spacing-4);
    display: block;
    color: var(--primary-blue);
}

.option-box h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--neutral-900);
    margin-bottom: var(--spacing-2);
}

.option-box p {
    font-size: var(--font-size-md);
    color: var(--neutral-700);
    margin-bottom: var(--spacing-4);
}

.option-box .details {
    font-size: var(--font-size-sm);
    color: var(--neutral-700);
    line-height: 1.8;
}

.option-box .details i {
    color: var(--primary-blue);
    margin-right: var(--spacing-2);
}

/* Draft container */
.draft-container {
    margin-top: var(--spacing-8);
    padding-top: var(--spacing-8);
    border-top: 1px solid var(--neutral-300);
}

/* Simple dropdown box */
.simple-dropdown-box {
    display: none;
    position: absolute;
    background-color: var(--neutral-100);
    border: 1px solid var(--neutral-400);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-lg);
    width: 300px;
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
}

.simple-dropdown-box.show {
    display: block;
    animation: slideDown 0.3s ease forwards;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Draft option */
.draft-option {
    padding: var(--spacing-4) var(--spacing-6);
    cursor: pointer;
    transition: var(--transition-fast);
    border-bottom: 1px solid var(--neutral-300);
    font-size: var(--font-size-md);
    color: var(--neutral-800);
}

.draft-option:last-child {
    border-bottom: none;
}

.draft-option:hover {
    background-color: var(--primary-light-blue);
    color: var(--primary-blue);
}

.draft-option.selected {
    background-color: var(--primary-light-blue);
    color: var(--primary-blue);
    font-weight: 500;
}

.draft-option.selected::after {
    content: '\f00c';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    color: var(--primary-blue);
    float: right;
}

/* Custom scrollbar */
.simple-dropdown-box::-webkit-scrollbar {
    width: 6px;
}

.simple-dropdown-box::-webkit-scrollbar-track {
    background: var(--neutral-200);
    border-radius: var(--border-radius-full);
}

.simple-dropdown-box::-webkit-scrollbar-thumb {
    background: var(--neutral-500);
    border-radius: var(--border-radius-full);
}

.simple-dropdown-box::-webkit-scrollbar-thumb:hover {
    background: var(--neutral-600);
}



/* Action Section */
.action-section {
    margin-top: auto;
    padding: var(--spacing-6) 0;
    border-top: 1px solid var(--neutral-300);
    background-color: var(--neutral-100);
    position: sticky;
    bottom: 0;
}

.action-buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--spacing-4);
    max-width: 900px;
    margin: 0 auto;
    width: 100%;
}

.primary-button,
.secondary-button {
    padding: var(--spacing-4) var(--spacing-8);
    border-radius: var(--border-radius-lg);
    font-size: var(--font-size-md);
    font-weight: 600;
    transition: var(--transition-normal);
    cursor: pointer;
    border: none;
    outline: none;
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    min-height: 50px;
}

.primary-button {
    background: linear-gradient(135deg, var(--primary-blue), var(--primary-pink));
    color: var(--neutral-100);
    box-shadow: var(--shadow-blue);
    flex: 1;
    max-width: 200px;
    justify-content: center;
}

.primary-button.disabled {
    background: var(--neutral-400);
    color: var(--neutral-600);
    cursor: not-allowed;
    box-shadow: none;
}

.primary-button.enabled:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 74, 173, 0.3);
}

.primary-button.loading {
    cursor: not-allowed;
}

.secondary-button {
    background-color: var(--neutral-100);
    color: var(--neutral-700);
    border: 2px solid var(--neutral-400);
}

.secondary-button:hover {
    background-color: var(--neutral-200);
    color: var(--neutral-900);
    border-color: var(--neutral-500);
    transform: translateY(-1px);
}

/* Selection Summary */
.selection-summary {
    margin-top: var(--spacing-4);
    opacity: 0;
    transform: translateY(-10px);
    transition: var(--transition-normal);
}

.summary-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-4);
    background-color: var(--primary-light-blue);
    border: 1px solid var(--primary-blue);
    border-radius: var(--border-radius-md);
    color: var(--primary-blue);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.summary-content i {
    color: var(--success);
}

/* Flash Messages */
.flash-messages {
    position: fixed;
    top: var(--spacing-4);
    right: var(--spacing-4);
    z-index: 9999;
    max-width: 400px;
}

.alert {
    padding: var(--spacing-4) var(--spacing-6);
    margin-bottom: var(--spacing-3);
    border-radius: var(--border-radius-lg);
    font-size: var(--font-size-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    box-shadow: var(--shadow-lg);
    position: relative;
    animation: slideInRight 0.3s ease-out;
}

.alert-success {
    background-color: #d1fae5;
    color: #065f46;
    border-left: 4px solid #10b981;
}

.alert-danger {
    background-color: #fee2e2;
    color: #b91c1c;
    border-left: 4px solid #ef4444;
}

.alert-warning {
    background-color: #fef3c7;
    color: #92400e;
    border-left: 4px solid #f59e0b;
}

.alert-info {
    background-color: #dbeafe;
    color: #1e40af;
    border-left: 4px solid #3b82f6;
}

.alert-close {
    position: absolute;
    top: var(--spacing-2);
    right: var(--spacing-2);
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: var(--spacing-1);
    border-radius: var(--border-radius-sm);
    opacity: 0.7;
    transition: var(--transition-fast);
}

.alert-close:hover {
    opacity: 1;
    background-color: rgba(0, 0, 0, 0.1);
}



/* Notifications */
.notification {
    position: fixed;
    top: var(--spacing-4);
    right: var(--spacing-4);
    background-color: var(--neutral-100);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-4) var(--spacing-6);
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    z-index: 9999;
    animation: slideInRight 0.3s ease-out;
    max-width: 400px;
}

.notification-error {
    border-left: 4px solid var(--error);
    color: var(--error);
}

.notification-warning {
    border-left: 4px solid var(--warning);
    color: var(--warning);
}

.notification-info {
    border-left: 4px solid var(--info);
    color: var(--info);
}

.notification button {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: var(--spacing-1);
    border-radius: var(--border-radius-sm);
    opacity: 0.7;
    transition: var(--transition-fast);
}

.notification button:hover {
    opacity: 1;
    background-color: rgba(0, 0, 0, 0.1);
}

/* NoScript Fallback */
.noscript-fallback {
    text-align: center;
    margin-top: var(--spacing-8);
    padding: var(--spacing-6);
    background-color: var(--warning);
    color: #92400e;
    border-radius: var(--border-radius-lg);
}

.noscript-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-4);
}

.noscript-content i {
    font-size: var(--font-size-3xl);
}

.noscript-content h3 {
    margin: 0;
    font-size: var(--font-size-xl);
}

.noscript-button {
    background-color: #92400e;
    color: var(--neutral-100);
    border: none;
    padding: var(--spacing-3) var(--spacing-6);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-md);
    font-weight: 600;
    cursor: pointer;
}

/* Decorative elements */
.container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 10% 10%, rgba(0, 74, 173, 0.03) 0%, transparent 30%),
        radial-gradient(circle at 90% 90%, rgba(205, 32, 139, 0.03) 0%, transparent 30%);
    pointer-events: none;
    z-index: -1;
    border-radius: var(--border-radius-lg);
}

/* Animations */
@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes fadeIn {
    0% { opacity: 0; transform: translateY(-10px); }
    100% { opacity: 1; transform: translateY(0); }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pulse-animation {
    animation: pulse 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-pulse {
    animation: pulse 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.progress-indicator {
    animation: fadeIn 0.8s ease-out forwards;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .page-wrapper {
        background: var(--neutral-100);
    }

    .container {
        padding: var(--spacing-6);
        max-width: none;
    }

    .title-section {
        margin: var(--spacing-4) 0 var(--spacing-6);
        padding: 0 var(--spacing-2);
    }

    .title-section h1 {
        font-size: var(--font-size-3xl);
        line-height: 1.3;
        margin-bottom: var(--spacing-3);
    }

    .title-section p {
        font-size: var(--font-size-md);
        padding: 0 var(--spacing-2);
    }

    .quick-stats {
        flex-direction: column;
        gap: var(--spacing-3);
        text-align: center;
    }

    .stat-item {
        justify-content: center;
    }

    .main-content {
        margin: 0;
    }

    .section-title {
        font-size: var(--font-size-xl);
        margin-bottom: var(--spacing-4);
    }

    .primary-option-card {
        margin-bottom: var(--spacing-3);
    }

    .option-header {
        padding: var(--spacing-4);
    }

    .option-icon {
        width: 50px;
        height: 50px;
        margin-right: var(--spacing-3);
    }

    .option-content h3 {
        font-size: var(--font-size-lg);
    }

    .option-content p {
        font-size: var(--font-size-sm);
    }

    .option-details {
        padding: 0 var(--spacing-4) var(--spacing-4);
    }

    .job-type-selection {
        grid-template-columns: 1fr;
        gap: var(--spacing-3);
    }

    .job-type-card {
        padding: var(--spacing-4);
    }

    .job-type-icon {
        width: 40px;
        height: 40px;
    }

    .job-type-info h4 {
        font-size: var(--font-size-md);
    }

    .job-type-info p {
        font-size: var(--font-size-xs);
    }

    .rework-item {
        padding: var(--spacing-4);
        margin-bottom: var(--spacing-3);
    }

    .rework-info h4 {
        font-size: var(--font-size-md);
    }

    .rework-status,
    .rework-description {
        font-size: var(--font-size-xs);
    }

    .rework-action {
        flex-direction: column;
        gap: var(--spacing-2);
    }

    .rework-badge {
        font-size: 0.6rem;
        padding: var(--spacing-1) var(--spacing-2);
    }



    .action-section {
        padding: var(--spacing-4) 0;
    }

    .action-buttons {
        flex-direction: column;
        gap: var(--spacing-3);
    }

    .primary-button,
    .secondary-button {
        width: 100%;
        max-width: none;
        padding: var(--spacing-4);
        font-size: var(--font-size-md);
    }

    .flash-messages {
        top: var(--spacing-2);
        right: var(--spacing-2);
        left: var(--spacing-2);
        max-width: none;
    }



    /* Progress indicator mobile adjustments */
    .progress-indicator {
        margin-bottom: var(--spacing-4);
        padding: 0 var(--spacing-2);
    }

    .progress-step {
        margin: 0 var(--spacing-1);
    }

    .step-number {
        width: 2rem;
        height: 2rem;
        font-size: var(--font-size-sm);
        margin-right: var(--spacing-1);
    }

    .step-label {
        font-size: var(--font-size-xs);
        display: none; /* Hide labels on very small screens */
    }

    .step-connector {
        min-width: 1rem;
        max-width: 2rem;
        margin: 0 var(--spacing-1);
    }
}

@media (max-width: 480px) {
    .container {
        margin: 5px;
        padding: var(--spacing-3);
        border-radius: var(--border-radius-sm);
    }

    .title-section {
        margin: var(--spacing-4) 0 var(--spacing-6);
    }

    .title-section h1 {
        font-size: var(--font-size-2xl);
        line-height: 1.2;
    }

    .title-section p {
        font-size: var(--font-size-sm);
    }

    .dropdown-section {
        padding: var(--spacing-3);
    }

    .option-box {
        padding: var(--spacing-4);
    }

    .option-box .icon {
        font-size: 1.25rem;
    }

    .option-box h3 {
        font-size: var(--font-size-md);
    }

    .option-box p {
        font-size: var(--font-size-xs);
    }

    .job-post-dropdown {
        padding: var(--spacing-3);
    }

    .job-post-text {
        font-size: var(--font-size-sm);
    }

    .buttons-wrapper {
        flex-direction: column-reverse;
        gap: var(--spacing-2);
    }

    .continue-button,
    .cancel-button {
        width: 100%;
        padding: var(--spacing-3);
    }

    /* Show step labels on very small screens in a compact way */
    .step-label {
        display: block;
        font-size: 0.6rem;
        line-height: 1;
    }

    .progress-step {
        flex-direction: column;
        align-items: center;
        margin: 0 var(--spacing-1);
    }

    .step-number {
        margin-right: 0;
        margin-bottom: var(--spacing-1);
        width: 1.75rem;
        height: 1.75rem;
        font-size: var(--font-size-sm);
    }
}

/* Progress indicator */
.progress-indicator {
    display: flex;
    justify-content: center;
    margin-bottom: var(--spacing-8);
}

.progress-step {
    display: flex;
    align-items: center;
    margin: 0 var(--spacing-4);
}

.step-number {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: var(--border-radius-full);
    background-color: var(--neutral-300);
    color: var(--neutral-700);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: var(--font-size-lg);
    margin-right: var(--spacing-2);
    transition: var(--transition-normal);
    box-shadow: var(--shadow-sm);
}

.step-number.active {
    background: linear-gradient(135deg, var(--primary-blue), var(--primary-pink));
    color: var(--neutral-100);
    box-shadow: var(--shadow-blue);
}

.step-label {
    font-size: var(--font-size-sm);
    color: var(--neutral-700);
    font-weight: 500;
}

.step-label.active {
    color: var(--primary-blue);
    font-weight: 600;
}

.step-connector {
    flex: 1;
    height: 2px;
    background-color: var(--neutral-300);
    margin: 0 var(--spacing-2);
    min-width: 2rem;
    max-width: 4rem;
}

.step-connector.active {
    background: linear-gradient(90deg, var(--primary-blue), var(--primary-pink));
}

/* Special positioning for the nested dropdown */
#dropdownOptions5 {
    position: absolute;
    left: 0;
    top: 100%;
    width: 100%;
    max-width: 300px;
    z-index: 1010;
}

/* Animation for the nested dropdown */
@keyframes slideFromRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Mobile-specific touch improvements */
@media (max-width: 768px) {
    /* Improve touch targets */
    .job-post-dropdown,
    .option-box,
    .draft-option,
    .continue-button,
    .cancel-button {
        min-height: 44px; /* iOS recommended touch target size */
        -webkit-tap-highlight-color: transparent;
    }

    /* Better touch feedback */
    .option-box:active {
        transform: translateY(-2px) scale(0.98);
        transition: transform 0.1s ease;
    }

    .job-post-dropdown:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
    }

    .continue-button:active,
    .cancel-button:active {
        transform: scale(0.95);
        transition: transform 0.1s ease;
    }

    /* Prevent zoom on input focus */
    input, select, textarea {
        font-size: 16px;
    }

    /* Smooth scrolling for mobile */
    html {
        scroll-behavior: smooth;
        -webkit-overflow-scrolling: touch;
    }

    /* Better dropdown positioning on mobile */
    .simple-dropdown-box {
        position: fixed !important;
        left: var(--spacing-2) !important;
        right: var(--spacing-2) !important;
        width: auto !important;
        max-height: 60vh;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }

    /* Mobile-friendly scrollbar */
    .simple-dropdown-box::-webkit-scrollbar {
        width: 8px;
    }

    .simple-dropdown-box::-webkit-scrollbar-thumb {
        background: var(--neutral-400);
        border-radius: var(--border-radius-full);
    }
}

/* Extra small mobile devices */
@media (max-width: 360px) {
    .container {
        margin: 2px;
        padding: var(--spacing-2);
    }

    .title-section h1 {
        font-size: var(--font-size-xl);
    }

    .option-box {
        padding: var(--spacing-3);
    }

    .option-box h3 {
        font-size: var(--font-size-sm);
    }

    .job-post-dropdown {
        padding: var(--spacing-2);
    }

    .buttons-wrapper {
        padding: 0 var(--spacing-2);
    }
}

/* Landscape orientation adjustments */
@media (max-width: 768px) and (orientation: landscape) {
    .container {
        margin: 5px;
        min-height: calc(100vh - 10px);
    }

    .title-section {
        margin: var(--spacing-4) 0 var(--spacing-6);
    }

    .title-section h1 {
        font-size: var(--font-size-2xl);
    }

    .progress-indicator {
        margin-bottom: var(--spacing-4);
    }

    .dropdown-section {
        padding: var(--spacing-3);
    }

    .button-container {
        padding: var(--spacing-3) 0;
    }
}




