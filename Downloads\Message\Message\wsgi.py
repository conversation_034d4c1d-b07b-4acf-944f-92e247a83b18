from app import app, socketio
import logging
import sys

# Configure enhanced logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

# Set up specific loggers
logger = logging.getLogger('socketio')
logger.setLevel(logging.INFO)

# Also log database connection issues
db_logger = logging.getLogger('mysql')
db_logger.setLevel(logging.INFO)

# Log Flask errors
flask_logger = logging.getLogger('flask')
flask_logger.setLevel(logging.INFO)

# Log werkzeug (HTTP server) errors
werkzeug_logger = logging.getLogger('werkzeug')
werkzeug_logger.setLevel(logging.INFO)

# Production application
application = app  # For WSGI servers like Gunicorn

if __name__ == "__main__":
    # Run with debug enabled for development
    socketio.run(
        app,
        host='0.0.0.0',
        port=8000,
        debug=True,
        use_reloader=False,  # Disable reloader to prevent duplicate Socket.IO instances
        allow_unsafe_werkzeug=True,  # Allow Werkzeug debugger
        log_output=True,  # Enable logging of Socket.IO events
        max_size=10 * 1024 * 1024  # Increase max packet size to 10MB
    )