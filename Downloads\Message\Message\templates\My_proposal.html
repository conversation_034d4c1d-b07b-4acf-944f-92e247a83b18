<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GigGenius - My Proposals</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <style>
        /* Variables */
        :root {
            --primary-blue: #003a8c;
            --primary-pink: #d41b8c;
            --white: #ffffff;
            --light-gray: #f8f9fa;
            --border-color: #e5e7eb;
            --text-dark: #333;
            --text-medium: #6b7280;
            --text-light: #ffffff;
        }

        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            background-color: #f8f9fa;
            color: #333;
            line-height: 1.5;
        }

        a {
            text-decoration: none;
            color: inherit;
        }

        ul {
            list-style: none;
        }

        /* Layout */
        .container {
            max-width: 2000px;
            margin: 0 auto;
            background-color: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .main-content {
            padding: 1.5rem 1rem 3rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        /* Navbar Styles */
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            height: 5rem;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .navbar-left {
            display: flex;
            align-items: center;
            gap: 2rem;
        }

        .logo {
            display: flex;
            align-items: center;
            text-decoration: none;
        }

        .logo img {
            width: 3.5rem;
            height: 3.5rem;
            border-radius: 50%;
        }

        .logo h1 {
            font-size: 1.7rem;
            font-weight: bold;
            color: var(--primary-pink);
            margin-left: 0.5rem;
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .nav-links a {
            color: #004AAD;
            text-decoration: none;
            font-size: 1.1rem;
            font-weight: 500;
        }

        .nav-links a:hover {
            color: #CD208B;
        }

        /* Dropdown styles */
        .nav-dropdown {
            position: relative;
            display: inline-block;
        }

        .nav-dropbtn {
            font-weight: 500;
            font-size: 1.1rem;
            color: #004AAD;
            background: transparent;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0;
        }

        .nav-dropbtn:hover {
            color: #CD208B;
        }

        .nav-dropdown-content {
            display: none;
            position: absolute;
            background-color: #fff;
            min-width: 200px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            border-radius: 8px;
            z-index: 1;
            padding: 0.5rem 0;
        }

        .nav-dropdown:hover .nav-dropdown-content {
            display: block;
        }

        .nav-dropdown-content a {
            color: #004AAD;
            padding: 0.75rem 1rem;
            text-decoration: none;
            display: block;
            font-size: 1rem;
        }

        .nav-dropdown-content a:hover {
            background-color: #f9f9f9;
            color: #CD208B;
        }

        /* Header actions styles */
        .header-actions {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-left: 2rem; /* Added to match testing.html spacing */
        }

        .search-wrapper {
            position: relative;
            display: none;
        }

        @media (min-width: 768px) {
            .search-wrapper {
                display: block;
                margin-right: 16px; /* Add space between search and notification */
            }
        }

        .header-search {
            padding: 0.5rem 1rem;
            padding-right: 2.5rem;
            border: 1px solid var(--border-color);
            border-radius: 20px;
            font-size: 0.875rem;
            width: 240px;
            background-color: #f5f5f5;
        }

        .search-wrapper i {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-medium);
            font-size: 0.875rem;
        }

        .profile-dropdown {
            position: relative;
        }

        .profile-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            overflow: hidden;
            display: none;
            cursor: pointer;
        }

        @media (min-width: 768px) {
            .profile-icon {
                display: block;
            }
        }

        .profile-icon img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .profile-dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background-color: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            width: 200px;
            padding: 0.5rem 0;
            display: none;
            z-index: 100;
        }

        .profile-dropdown-menu a {
            display: block;
            padding: 0.75rem 1rem;
            color: #333;
            font-size: 0.875rem;
            transition: background-color 0.2s;
        }

        .profile-dropdown-menu a:hover {
            background-color: #f3f4f6;
        }

        .profile-dropdown-menu.active {
            display: block;
        }

        .notification-btn {
            position: relative;
            background: none;
            border: none;
            cursor: pointer;
            width: 40px;
            height: 40px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px; /* Add space between notification and profile */
        }

        .notification-btn:hover {
            background-color: #f3f4f6;
        }

        .notification-indicator {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 8px;
            height: 8px;
            background-color: var(--primary-pink);
            border-radius: 50%;
        }

        /* Notification Dropdown Styles */
        .notification-dropdown {
            position: relative;
        }

        .notification-dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background-color: white;
            min-width: 320px;
            max-height: 400px;
            overflow-y: auto;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            border-radius: 8px;
            padding: 0;
            z-index: 100;
            display: none;
            margin-top: 0.5rem;
            border: 1px solid #e5e7eb;
        }

        .notification-dropdown-menu.active {
            display: block;
        }

        .notification-header {
            padding: 1rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .notification-header h3 {
            margin: 0;
            font-size: 1rem;
            font-weight: 600;
            color: #333;
        }

        .notification-header .mark-all-read {
            font-size: 0.75rem;
            color: #064dac;
            cursor: pointer;
            background: none;
            border: none;
            padding: 0;
        }

        .notification-header .mark-all-read:hover {
            text-decoration: underline;
        }

        .notification-list {
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .notification-item {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #f3f4f6;
            cursor: pointer;
            transition: background-color 0.2s;
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
        }

        .notification-item:hover {
            background-color: #f9fafb;
        }

        .notification-item:last-child {
            border-bottom: none;
        }

        .notification-item.unread {
            background-color: #f0f7ff;
        }

        .notification-item.unread:hover {
            background-color: #e1f0ff;
        }

        .notification-icon {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .notification-icon i {
            color: #064dac;
            font-size: 1rem;
        }

        .notification-content {
            flex: 1;
        }

        .notification-text {
            font-size: 0.875rem;
            color: #333;
            margin-bottom: 0.25rem;
            line-height: 1.4;
        }

        .notification-text strong {
            color: #003a8c;
            font-weight: 600;
        }

        .notification-time {
            font-size: 0.75rem;
            color: #6b7280;
        }

        .notification-footer {
            padding: 0.75rem 1rem;
            text-align: center;
            border-top: 1px solid #e5e7eb;
        }

        .notification-footer a {
            font-size: 0.875rem;
            color: #064dac;
            text-decoration: none;
        }

        .notification-footer a:hover {
            text-decoration: underline;
        }

        .no-notifications {
            padding: 2rem 1rem;
            text-align: center;
            color: #6b7280;
            font-size: 0.875rem;
        }

        .mobile-menu-btn {
            background: none;
            border: none;
            cursor: pointer;
            width: 40px;
            height: 40px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        @media (min-width: 768px) {
            .mobile-menu-btn {
                display: none;
            }
        }

        .mobile-menu-btn:hover {
            background-color: #f3f4f6;
        }

        .mobile-menu {
            display: none;
            position: absolute;
            top: 60px;
            right: 1rem;
            background-color: white;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            width: 200px;
            z-index: 20;
            border: 1px solid var(--border-color);
        }

        @media (min-width: 768px) {
            .mobile-menu {
                display: none !important;
            }
        }

        .mobile-menu.active {
            display: block;
        }

        .mobile-menu a {
            display: block;
            padding: 0.75rem 1rem;
            color: var(--text-dark);
            font-size: 0.875rem;
            border-bottom: 1px solid #f3f4f6;
        }

        .mobile-menu a:last-child {
            border-bottom: none;
        }

        .mobile-menu a:hover {
            background-color: #f9fafb;
        }

        /* Status Badge Styles */
        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .status-pending {
            background-color: #fef3c7;
            color: #92400e;
        }

        .status-accepted {
            background-color: #d1fae5;
            color: #065f46;
        }

        .status-rejected {
            background-color: #fee2e2;
            color: #991b1b;
        }

        .status-messaged {
            background-color: #dbeafe;
            color: #1e40af;
        }

        /* Client Info Styles */
        .client-info {
            margin-top: 0.25rem;
        }

        .client-name {
            font-size: 0.75rem;
            color: var(--text-medium);
        }

        /* No Proposals Styles */
        .no-proposals {
            text-align: center;
            padding: 3rem 1rem;
            color: var(--text-medium);
        }

        .no-proposals i {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #d1d5db;
        }

        .no-proposals h4 {
            font-size: 1.25rem;
            margin-bottom: 0.5rem;
            color: var(--text-dark);
        }

        .no-proposals p {
            margin-bottom: 1.5rem;
        }

        .find-jobs-btn {
            display: inline-block;
            background-color: var(--primary-blue);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: background-color 0.2s;
        }

        .find-jobs-btn:hover {
            background-color: #002a6b;
        }

        /* Page Header */
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .page-header h1 {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-dark);
        }

        /* Stats Card */
        .stats-card {
            background-color: white;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            display: flex;
            justify-content: space-between;
            gap: 1rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .stat-item {
            flex: 1;
            text-align: center;
            padding: 1rem;
            border-right: 1px solid var(--border-color);
        }

        .stat-item:last-child {
            border-right: none;
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-blue);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--text-medium);
        }

        /* Promo Card */
        .promo-card {
            background-color: var(--primary-blue);
            color: var(--white);
            padding: 1.5rem;
            border-radius: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .promo-content {
            flex: 1;
        }

        .promo-content h2 {
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .promo-content p {
            font-size: 0.875rem;
            margin-bottom: 1rem;
        }

        .watch-button {
            background-color: var(--white);
            color: var(--primary-blue);
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .watch-button:hover {
            background-color: #f3f4f6;
        }

        /* Tabs */
        .tabs {
            display: flex;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 1.5rem;
        }

        .tab-button {
            padding: 0.75rem 1.5rem;
            background: none;
            border: none;
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--text-medium);
            cursor: pointer;
            position: relative;
        }

        .tab-button.active {
            color: var(--primary-blue);
        }

        .tab-button.active::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: var(--primary-blue);
        }

        /* Section Styles */
        .section {
            background-color: var(--white);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            margin-bottom: 1.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }

        .section-header {
            padding: 1.25rem;
            background-color: #f9fafb;
            border-bottom: 1px solid var(--border-color);
        }

        .section-title {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-dark);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .section-title .count {
            color: var(--primary-blue);
        }

        .info-icon {
            color: var(--text-medium);
            font-size: 0.875rem;
            cursor: help;
        }

        .section-content {
            padding: 0;
        }

        /* Proposal Item */
        .proposal-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.25rem;
            border-bottom: 1px solid var(--border-color);
        }

        .proposal-item:last-child {
            border-bottom: none;
        }

        .proposal-info {
            display: flex;
            align-items: center;
            flex: 1;
            gap: 1.5rem;
        }

        .date-container {
            min-width: 100px;
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .date {
            font-weight: 500;
            color: var(--text-dark);
        }

        .timestamp {
            font-size: 0.75rem;
            color: var(--text-medium);
        }

        .work-details {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
            flex: 1;
        }

        .proposal-title {
            color: var(--primary-blue);
            font-weight: 500;
            font-size: 0.875rem;
        }

        .proposal-title:hover {
            text-decoration: underline;
        }

        .proposal-budget {
            font-size: 0.75rem;
            color: var(--text-medium);
        }

        .action-profile-button {
            padding: 0.5rem 1rem;
            background-color: var(--primary-pink);
            color: var(--white);
            border: none;
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
            min-width: 100px;
            text-align: center;
        }

        .action-profile-button:hover {
            background-color: #b91c77;
        }

        /* View More Button */
        .view-more-container {
            text-align: center;
            padding: 1rem 0;
        }

        .view-more-button {
            background: none;
            border: none;
            color: var(--primary-blue);
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            padding: 0.5rem 1rem;
        }

        .view-more-button:hover {
            text-decoration: underline;
        }

        /* Referral Item */
        .referral-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.25rem;
            border-bottom: 1px solid var(--border-color);
        }

        .referral-item:last-child {
            border-bottom: none;
        }

        .referral-info {
            display: flex;
            align-items: center;
            gap: 1.25rem;
        }

        .referral-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
        }

        .referral-details {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .date-info {
            font-size: 0.75rem;
            color: var(--text-medium);
        }

        .employment-history {
            font-size: 0.75rem;
            color: var(--text-medium);
            font-style: italic;
        }

        .referral-name {
            font-weight: 500;
            color: var(--primary-blue);
            font-size: 0.875rem;
        }

        .expertise {
            font-size: 0.75rem;
            color: var(--text-dark);
            font-weight: 500;
        }

        .referral-status {
            font-size: 0.75rem;
            padding: 0.125rem 0.5rem;
            border-radius: 12px;
            display: inline-block;
            width: fit-content;
            background-color: #e8f5e9;
            color: #2e7d32;
        }

        .referral-status.pending {
            background-color: #fff3e0;
            color: #ef6c00;
        }

        .referral-stats {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 0.75rem;
        }

        .commission {
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--text-dark);
        }

        .contact-button {
            padding: 0.5rem 1rem;
            background-color: var(--primary-blue);
            color: var(--white);
            border: none;
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .contact-button:hover {
            background-color: #053d8a;
        }

        /* Table Styles */
        .offers-table {
            width: 100%;
            border-collapse: collapse;
        }

        .offers-table th {
            background-color: #f9fafb;
            padding: 0.75rem 1.25rem;
            text-align: left;
            font-weight: 600;
            color: var(--text-dark);
            font-size: 0.875rem;
            border-bottom: 1px solid var(--border-color);
        }

        .offers-table td {
            padding: 1rem 1.25rem;
            border-bottom: 1px solid var(--border-color);
            color: var(--text-dark);
            font-size: 0.875rem;
        }

        .offers-table tr:hover {
            background-color: #f9fafb;
        }

        .job-link {
            display: block;
            color: var(--primary-blue);
            font-weight: 500;
            margin-bottom: 0.25rem;
        }

        .job-link:hover {
            text-decoration: underline;
        }

        .company {
            display: block;
            font-size: 0.75rem;
            color: var(--text-medium);
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
            display: inline-block;
        }

        .status-badge.open {
            background-color: #e8f5e9;
            color: #2e7d32;
        }

        .status-badge.closed {
            background-color: #ffebee;
            color: #c62828;
        }

        .action-btn {
            padding: 0.375rem 1rem;
            background-color: var(--primary-blue);
            color: var(--white);
            border: none;
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .action-btn:hover {
            background-color: #053d8a;
        }

        /* Interview Item */
        .interview-item {
            padding: 1.25rem;
            border-bottom: 1px solid var(--border-color);
        }

        .interview-item:last-child {
            border-bottom: none;
        }

        .interview-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.75rem;
        }

        .interview-details {
            display: flex;
            justify-content: space-between;
            color: var(--text-medium);
            font-size: 0.75rem;
            padding-top: 0.75rem;
            border-top: 1px solid var(--border-color);
        }

        .status {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
            background-color: #ffebee;
            color: #c62828;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background-color: white;
            padding: 1.5rem;
            border-radius: 8px;
            width: 80%;
            max-width: 800px;
            position: relative;
            margin: 0 auto;
        }

        .close-button {
            position: absolute;
            right: 1.25rem;
            top: 0.75rem;
            font-size: 1.5rem;
            cursor: pointer;
            color: #666;
        }

        .close-button:hover {
            color: #333;
        }

        #safetyVideo {
            margin-top: 1.25rem;
            border-radius: 6px;
            width: 100%;
            height: auto;
            max-height: 80vh;
        }

        .modal[style*="display: block"] {
            display: flex !important;
        }

        /* Footer Styles */
        footer {
            background: #003a8c;
            padding: 2rem 5%;
            align-items: center;
        }

        .footer-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 3rem;
        }

        .footer-column h3 {
            margin-bottom: 1rem;
            color: var(--text-light);
        }

        .footer-column a {
            display: block;
            color: var(--text-light);
            text-decoration: none;
            margin-bottom: 0.5rem;
            transition: text-decoration 0.3s ease;
        }

        .footer-column a:hover {
            text-decoration: underline;
        }

        .footer-bottom {
            color: var(--text-light);
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 2rem 0;
            flex-wrap: wrap;
        }

        .footer-bottom p {
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 400;
        }

        .social-icons {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .social-icons a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            margin: 0;
        }

        .social-icons .bi {
            font-size: 1.2rem;
            color: var(--text-light);
            transition: transform 0.3s ease, color 0.3s ease;
        }

        .social-icons a:hover {
            background-color: var(--primary-pink);
            transform: translateY(-3px);
        }

        /* Responsive Styles */
        @media (max-width: 768px) {
            .header-nav {
                display: none;
            }

            .header-search {
                width: 100%;
                max-width: 200px;
            }

            .proposal-item, .referral-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }

            .proposal-info {
                width: 100%;
            }

            .profile-button, .contact-button {
                width: 100%;
            }

            .referral-info {
                width: 100%;
            }

            .referral-stats {
                width: 100%;
                align-items: flex-start;
            }

            .offers-table {
                display: block;
                overflow-x: auto;
            }

            .stats-card {
                flex-direction: column;
            }

            .stat-item {
                border-right: none;
                border-bottom: 1px solid var(--border-color);
                padding: 0.75rem 0;
            }

            .stat-item:last-child {
                border-bottom: none;
            }

            .footer-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 2rem;
            }

            .footer-bottom {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }

            .footer-bottom p {
                justify-content: center;
                font-size: 0.9rem;
            }

            .social-icons {
                justify-content: center;
            }
        }

        @media (max-width: 480px) {
            .footer-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }
        }

        @media (min-width: 640px) {
            .main-content {
                padding: 1.5rem 1.5rem;
            }
        }

        @media (min-width: 1024px) {
            .main-content {
                padding: 2rem 2rem;
            }
        }

        /* Section content expanded/collapsed states */
        .section-content .proposal-item:nth-child(n+3) {
            display: none;
        }

        .section-content.expanded .proposal-item {
            display: flex !important;
        }

        /* Footer */
        footer {
            background: #003a8c;
            padding: 2rem 5%;
            align-items: center;
        }

        .footer-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 3rem;
        }

        .footer-column h3 {
            margin-bottom: 1rem;
            color: var(--text-light);
        }

        .footer-column a {
            display: block;
            color: var(--text-light);
            text-decoration: none;
            margin-bottom: 0.5rem;
            transition: text-decoration 0.3s ease;
        }

        .footer-column a:hover {
            text-decoration: underline;
        }

        .footer-bottom {
            color: var(--text-light);
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 2rem 0;
            flex-wrap: wrap;
            font-family: 'Poppins', sans-serif;
        }

        .footer-bottom p {
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 400;
        }

        .social-icons {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .social-icons a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            margin: 0;
        }

        .social-icons .bi {
            font-size: 1.2rem;
            color: var(--text-light);
            transition: transform 0.3s ease, color 0.3s ease;
        }

        .social-icons a:hover {
            background-color: var(--primary-pink);
            transform: translateY(-3px);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .footer-bottom {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }

            .footer-bottom p {
                justify-content: center;
                font-size: 0.9rem; /* Slightly smaller font size on mobile */
            }

            .social-icons {
                justify-content: center;
            }
        }

        .footer-bottom a {
            color: var(--text-light);
            margin: 0 10px;
            text-decoration: none;
        }

        .footer-bottom a:hover {
            text-decoration: underline;
        }

        .footer-bottom .social-icons img {
            width: 24px;
            height: 24px;
            margin: 0 5px;
        }

/* Navbar Styles */
.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 2rem;
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    height: 5rem;
}

.navbar-left {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.logo {
    display: flex;
    align-items: center;
    text-decoration: none;
}

.logo img {
    width: 3.5rem;
    height: 3.5rem;
    border-radius: 50%;
    border: 2px solid #004AAD;
    object-fit: cover;
}

.logo h1 {
    font-size: 1.7rem;
    font-weight: bold;
    color: #CD208B;
    margin-left: 0.5rem;
}

.nav-links {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.nav-links a {
    color: #004AAD;
    text-decoration: none;
    font-size: 1.1rem;
    font-weight: 500;
}

.nav-links a:hover {
    color: #CD208B;
}

.nav-dropdown {
    position: relative;
    display: inline-block;
}

.nav-dropbtn {
    font-weight: 500;
    font-size: 1.1rem;
    color: #004AAD;
    background: none;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0;
}

.nav-dropbtn:hover {
    color: #CD208B;
}

.nav-dropdown-content {
    display: none;
    position: absolute;
    background-color: #fff;
    min-width: 200px;
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
    border-radius: 8px;
    z-index: 1001;
    top: 100%;
    left: 0;
}

.nav-dropdown-content a {
    color: #004AAD;
    padding: 12px 16px;
    text-decoration: none;
    display: block;
    font-size: 1rem;
}

.nav-dropdown-content a:hover {
    background-color: #f9f9f9;
    color: #CD208B;
}

.nav-dropdown:hover .nav-dropdown-content {
    display: block;
}

/* Right section container */
.right-section {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

/* Search container */
.search-container {
    display: flex;
    align-items: center;
}

.search-type-select {
    position: relative;
}

.search-type-button {
    height: 2.5rem;
    background: white;
    border: 1px solid #004AAD;
    border-right: none;
    border-radius: 8px 0 0 8px;
    padding: 0 1rem;
    color: #004AAD;
    font-size: 1rem;
    display: flex;
    align-items: center;
    cursor: pointer;
}

.search-bar {
    height: 2.5rem;
    display: flex;
    align-items: center;
    background: white;
    border: 1px solid #004AAD;
    border-radius: 0 8px 8px 0;
    width: 200px;
}

.search-bar input {
    border: none;
    outline: none;
    padding: 0 0.5rem;
    width: 100%;
    height: 100%;
    font-size: 1rem;
}

.search-bar .icon {
    color: #004AAD;
    padding: 0 0.5rem;
}

/* Auth buttons container */
.auth-buttons {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

/* Notification icon */
.notification-icon {
    position: relative;
    cursor: pointer;
}

.notification-icon i {
    font-size: 1.5rem;
    color: #004AAD;
}

.notification-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: #CD208B;
    color: white;
    border-radius: 50%;
    padding: 0.1rem 0.4rem;
    font-size: 0.8rem;
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Profile button */
.profile-button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    cursor: pointer;
    border: 2px solid #004AAD;
}

.profile-button img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Profile dropdown */
.profile-dropdown {
    position: relative;
    display: inline-block;
}

.profile-dropdown-content {
    display: none;
    position: absolute;
    right: 0;
    top: 50px;
    background-color: #fff;
    min-width: 200px;
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
    border-radius: 8px;
    z-index: 1001;
}

.profile-dropdown-content a {
    color: #004AAD;
    padding: 12px 16px;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1rem;
}

.profile-dropdown-content a i {
    width: 20px;
    text-align: center;
}

.profile-dropdown-content a:hover {
    background-color: #f9f9f9;
    color: #CD208B;
}

.dropdown-divider {
    height: 1px;
    background-color: #eee;
    margin: 8px 0;
}

.logout-option {
    color: #dc3545 !important;
}

.logout-option:hover {
    background-color: #fff5f5 !important;
    color: #dc3545 !important;
}

/* Show dropdown on click */
.profile-dropdown-content.show {
    display: block;
}
    </style>
</head>
<body>
    <div class="container">
        <!-- Header Section -->
        <nav class="navbar">
            <div class="navbar-left">
                <a href="{{ url_for('landing_page') }}" class="logo">
                    <img src="{{ url_for('static', filename='img/logo.png') }}" alt="GigGenius Logo">
                    <h1>GigGenius</h1>
                </a>
                <div class="nav-links">
                    <a href="{{ url_for('genius_page') }}">Find Gigs</a>
                    <a href="{{ url_for('my_proposal') }}">Proposals</a>

                    <div class="nav-dropdown">
                        <button class="nav-dropbtn">Contracts
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="nav-dropdown-content">
                            <a href="{{ url_for('tracker') }}">Log Works</a>
                            <a href="{{ url_for('landing_page') }}">Work Diary</a>
                        </div>
                    </div>

                    <div class="nav-dropdown">
                        <button class="nav-dropbtn">Earnings
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="nav-dropdown-content">
                            <a href="{{ url_for('billing_and_earnings') }}">Billings and Earnings</a>
                            <a href="{{ url_for('withdraw_earnings') }}">Withdraw Earnings</a>
                            <a href="{{ url_for('tax_info') }}">Tax Info</a>
                        </div>
                    </div>

                    <a href="{{ url_for('landing_page') }}">Messages</a>
                </div>
            </div>
            <div class="right-section">
                <div class="search-container">
                    <div class="search-type-select">
                        <button class="search-type-button">
                            <span>Gigs</span>
                        </button>
                    </div>
                    <div class="search-bar">
                        <input type="text" placeholder="Search...">
                        <i class="fas fa-search icon"></i>
                    </div>
                </div>
                <div class="auth-buttons">
                    <div class="notification-icon">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </div>
                    <div class="profile-dropdown">
                        <div class="profile-button">
                            <img src="{{ genius.profile_picture_url if genius else url_for('static', filename='img/default_profile.png') }}" alt="Profile Picture">
                        </div>
                        <div class="profile-dropdown-content">
                            <a href="{{ url_for('genius_profile') }}">
                                <i class="fas fa-user"></i> My Profile
                            </a>
                            <a href="{{ url_for('withdraw_earnings', section='profile-settings') }}">
                                <i class="fas fa-cog"></i> Settings
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="{{ url_for('logout') }}" class="logout-option">
                                <i class="fas fa-sign-out-alt"></i> Log Out
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

    <div class="mobile-menu" id="mobileMenu">
        <a href="{{ url_for('genius_page') }}">Find Gigs</a>
        <a href="{{ url_for('my_proposal') }}">Proposals</a>
        <a href="#">Contracts</a>
        <a href="{{ url_for('tracker') }}">Log Works</a>
        <a href="#">Earnings</a>
        <a href="{{ url_for('billing_and_earnings') }}">Billings and Earnings</a>
        <a href="{{ url_for('withdraw_earnings') }}">Withdraw Earnings</a>
        <a href="{{ url_for('tax_info') }}">Tax Info</a>
        <a href="{{ url_for('landing_page') }}">Messages</a>
        <a href="{{ url_for('genius_profile') }}">Profile</a>
    </div>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Page Header -->
            <header class="page-header">
                <h1>My proposals</h1>
            </header>

            <!-- Stats Card -->
            <div class="stats-card">
                <div class="stat-item">
                    <div class="stat-value">{{ stats.total_proposals }}</div>
                    <div class="stat-label">Total Proposals</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">{{ stats.active_offers }}</div>
                    <div class="stat-label">Active Offers</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">{{ stats.success_rate }}%</div>
                    <div class="stat-label">Success Rate</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${{ "{:,.0f}".format(stats.potential_earnings) }}</div>
                    <div class="stat-label">Potential Earnings</div>
                </div>
            </div>

            <!-- Promo Card -->
            <div class="promo-card">
                <div class="promo-content">
                    <h2>Earn More with GigGenius' Affiliate  Program </h2>
                    <p>Join our Affiliate Program and earn 0.5% commission on every freelancer payout and 1% on every business owner's payment.</p>
                    <button class="watch-button">Refer now</button>
                </div>
            </div>

            <!-- Tabs -->
            <div class="tabs">
                <button class="tab-button active">Active</button>
                <button class="tab-button">Referrals</button>
                <button class="tab-button">Archived</button>
            </div>

            <!-- Active Tab Content -->
            <div id="activeTab">
                <!-- Offers Section -->
                <div class="section">
                    <div class="section-header">
                        <h3 class="section-title">
                            Offers <span class="count">(6)</span>
                            <i class="fas fa-info-circle info-icon"></i>
                        </h3>
                    </div>
                    <div class="section-content">
                        <!-- First item (always visible) -->
                        <div class="proposal-item">
                            <div class="proposal-info">
                                <div class="date-container">
                                    <span class="date">Feb 16, 2024</span>
                                    <span class="timestamp">2 hours ago</span>
                                </div>
                                <div class="work-details">
                                    <a href="#" class="proposal-title">Senior React Native Developer</a>
                                    <span class="proposal-budget">$55-65/hr</span>
                                </div>
                            </div>
                            <button class="action-profile-button">View Offer</button>
                        </div>

                        <!-- Second item (always visible) -->
                        <div class="proposal-item">
                            <div class="proposal-info">
                                <div class="date-container">
                                    <span class="date">Feb 16, 2024</span>
                                    <span class="timestamp">3 hours ago</span>
                                </div>
                                <div class="work-details">
                                    <a href="#" class="proposal-title">AWS Cloud Architecture Expert</a>
                                    <span class="proposal-budget">Fixed Price - $4,000</span>
                                </div>
                            </div>
                            <button class="action-profile-button">View Offer</button>
                        </div>

                        <!-- Additional items (hidden initially) -->
                        <div class="proposal-item">
                            <div class="proposal-info">
                                <div class="date-container">
                                    <span class="date">Feb 16, 2024</span>
                                    <span class="timestamp">4 hours ago</span>
                                </div>
                                <div class="work-details">
                                    <a href="#" class="proposal-title">iOS Swift Developer</a>
                                    <span class="proposal-budget">$50-60/hr</span>
                                </div>
                            </div>
                            <button class="action-profile-button">View Offer</button>
                        </div>

                        <div class="proposal-item">
                            <div class="proposal-info">
                                <div class="date-container">
                                    <span class="date">Feb 16, 2024</span>
                                    <span class="timestamp">5 hours ago</span>
                                </div>
                                <div class="work-details">
                                    <a href="#" class="proposal-title">Python Django Backend Developer</a>
                                    <span class="proposal-budget">$45-55/hr</span>
                                </div>
                            </div>
                            <button class="action-profile-button">View Offer</button>
                        </div>

                        <div class="proposal-item">
                            <div class="proposal-info">
                                <div class="date-container">
                                    <span class="date">Feb 15, 2024</span>
                                    <span class="timestamp">1 day ago</span>
                                </div>
                                <div class="work-details">
                                    <a href="#" class="proposal-title">Full Stack JavaScript Developer - MERN Stack</a>
                                    <span class="proposal-budget">$50-60/hr</span>
                                </div>
                            </div>
                            <button class="action-profile-button">View Offer</button>
                        </div>

                        <div class="proposal-item">
                            <div class="proposal-info">
                                <div class="date-container">
                                    <span class="date">Feb 15, 2024</span>
                                    <span class="timestamp">1 day ago</span>
                                </div>
                                <div class="work-details">
                                    <a href="#" class="proposal-title">UI/UX Designer for E-commerce Platform</a>
                                    <span class="proposal-budget">Fixed Price - $3,500</span>
                                </div>
                            </div>
                            <button class="action-profile-button">View Offer</button>
                        </div>

                        <!-- View More button -->
                        <div class="view-more-container">
                            <button class="view-more-button">View More</button>
                        </div>
                    </div>
                </div>

                <!-- Invitations to Interview Section -->
                <div class="section">
                    <div class="section-header">
                        <h3 class="section-title">
                            Invitations to Interview <span class="count">(5)</span>
                            <i class="fas fa-info-circle info-icon"></i>
                        </h3>
                    </div>
                    <div class="section-content">
                        <div class="proposal-item">
                            <div class="proposal-info">
                                <div class="date-container">
                                    <span class="date">Feb 15, 2024</span>
                                    <span class="timestamp">5 hours ago</span>
                                </div>
                                <div class="work-details">
                                    <a href="#" class="proposal-title">UI/UX Designer for Mobile App</a>
                                    <span class="proposal-budget">$40-45/hr</span>
                                </div>
                            </div>
                            <button class="action-profile-button">View Invitation</button>
                        </div>
                        <div class="proposal-item">
                            <div class="proposal-info">
                                <div class="date-container">
                                    <span class="date">Feb 14, 2024</span>
                                    <span class="timestamp">1 day ago</span>
                                </div>
                                <div class="work-details">
                                    <a href="#" class="proposal-title">Frontend Developer - Vue.js Expert</a>
                                    <span class="proposal-budget">$35-40/hr</span>
                                </div>
                            </div>
                            <button class="action-profile-button">View Invitation</button>
                        </div>
                        <div class="proposal-item">
                            <div class="proposal-info">
                                <div class="date-container">
                                    <span class="date">Feb 16, 2024</span>
                                    <span class="timestamp">3 hours ago</span>
                                </div>
                                <div class="work-details">
                                    <a href="#" class="proposal-title">DevOps Engineer - Kubernetes Specialist</a>
                                    <span class="proposal-budget">$50-60/hr</span>
                                </div>
                            </div>
                            <button class="action-profile-button">View Invitation</button>
                        </div>
                        <div class="proposal-item">
                            <div class="proposal-info">
                                <div class="date-container">
                                    <span class="date">Feb 16, 2024</span>
                                    <span class="timestamp">6 hours ago</span>
                                </div>
                                <div class="work-details">
                                    <a href="#" class="proposal-title">Flutter Mobile App Developer</a>
                                    <span class="proposal-budget">$45-50/hr</span>
                                </div>
                            </div>
                            <button class="action-profile-button">View Invitation</button>
                        </div>
                        <div class="proposal-item">
                            <div class="proposal-info">
                                <div class="date-container">
                                    <span class="date">Feb 15, 2024</span>
                                    <span class="timestamp">1 day ago</span>
                                </div>
                                <div class="work-details">
                                    <a href="#" class="proposal-title">Blockchain Developer - Smart Contracts</a>
                                    <span class="proposal-budget">$60-70/hr</span>
                                </div>
                            </div>
                            <button class="action-profile-button">View Invitation</button>
                        </div>
                        <div class="view-more-container">
                            <button class="view-more-button">View More</button>
                        </div>
                    </div>
                </div>

                <!-- Active Proposals Section -->
                <div class="section">
                    <div class="section-header">
                        <h3 class="section-title">
                            Active Proposals <span class="count">(4)</span>
                            <i class="fas fa-info-circle info-icon"></i>
                        </h3>
                    </div>
                    <div class="section-content">
                        <div class="proposal-item">
                            <div class="proposal-info">
                                <div class="date-container">
                                    <span class="date">Feb 14, 2024</span>
                                    <span class="timestamp">1 day ago</span>
                                </div>
                                <div class="work-details">
                                    <a href="#" class="proposal-title">Python Backend Developer - Django</a>
                                    <span class="proposal-budget">$45-50/hr</span>
                                </div>
                            </div>
                            <button class="action-profile-button">View Proposal</button>
                        </div>
                        <div class="proposal-item">
                            <div class="proposal-info">
                                <div class="date-container">
                                    <span class="date">Feb 13, 2024</span>
                                    <span class="timestamp">2 days ago</span>
                                </div>
                                <div class="work-details">
                                    <a href="#" class="proposal-title">React Native Mobile Developer</a>
                                    <span class="proposal-budget">Fixed Price - $3,000</span>
                                </div>
                            </div>
                            <button class="action-profile-button">View Proposal</button>
                        </div>
                        <div class="proposal-item">
                            <div class="proposal-info">
                                <div class="date-container">
                                    <span class="date">Feb 16, 2024</span>
                                    <span class="timestamp">4 hours ago</span>
                                </div>
                                <div class="work-details">
                                    <a href="#" class="proposal-title">GraphQL API Development</a>
                                    <span class="proposal-budget">$40-45/hr</span>
                                </div>
                            </div>
                            <button class="action-profile-button">View Proposal</button>
                        </div>
                        <div class="proposal-item">
                            <div class="proposal-info">
                                <div class="date-container">
                                    <span class="date">Feb 15, 2024</span>
                                    <span class="timestamp">1 day ago</span>
                                </div>
                                <div class="work-details">
                                    <a href="#" class="proposal-title">Next.js E-commerce Platform</a>
                                    <span class="proposal-budget">Fixed Price - $5,000</span>
                                </div>
                            </div>
                            <button class="action-profile-button">View Proposal</button>
                        </div>
                        <div class="view-more-container">
                            <button class="view-more-button">View More</button>
                        </div>
                    </div>
                </div>

                <!-- Submitted Proposals Section -->
                <div class="section">
                    <div class="section-header">
                        <h3 class="section-title">
                            Submitted Proposals <span class="count">({{ proposals|length }})</span>
                            <i class="fas fa-info-circle info-icon"></i>
                        </h3>
                    </div>
                    <div class="section-content">
                        {% if proposals %}
                            {% for proposal in proposals %}
                            <div class="proposal-item">
                                <div class="proposal-info">
                                    <div class="date-container">
                                        <span class="date">{{ proposal.created_at_formatted }}</span>
                                        <span class="timestamp">
                                            <span class="status-badge status-{{ proposal.status.lower() }}">{{ proposal.status.title() }}</span>
                                        </span>
                                    </div>
                                    <div class="work-details">
                                        <a href="#" class="proposal-title">{{ proposal.job_title }}</a>
                                        <span class="proposal-budget">
                                            {% if proposal.budget_type == 'hourly' %}
                                                ${{ proposal.budget_amount }}/hr
                                            {% else %}
                                                Fixed Price - ${{ "{:,.0f}".format(proposal.budget_amount) }}
                                            {% endif %}
                                        </span>
                                        <div class="client-info">
                                            <span class="client-name">Client: {{ proposal.client_display_name }}</span>
                                        </div>
                                    </div>
                                </div>
                                <button class="action-profile-button">View Details</button>
                            </div>
                            {% endfor %}
                        {% else %}
                            <div class="no-proposals">
                                <i class="fas fa-inbox"></i>
                                <h4>No proposals yet</h4>
                                <p>Start applying to jobs to see your proposals here.</p>
                                <a href="{{ url_for('find_gigs') }}" class="find-jobs-btn">Find Jobs</a>
                            </div>
                        {% endif %}

                        {% if proposals|length > 6 %}
                        <div class="view-more-container">
                            <button class="view-more-button">View More</button>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Referrals Tab Content -->
            <div id="referralsTab" style="display: none;">
                <!-- Freelancers You Recruit Section -->
                <div class="section">
                    <div class="section-header">
                        <h3 class="section-title">
                            Freelancers You Recruit <span class="count">(11)</span>
                            <i class="fas fa-info-circle info-icon"></i>
                        </h3>
                    </div>
                    <div class="section-content">
                        <div class="referral-item">
                            <div class="referral-info">
                                <img src="{{ url_for('static', filename='img/default_profile.png') }}" alt="Profile Picture" class="referral-avatar">
                                <div class="referral-details">
                                    <div class="date-info">
                                        <span class="date-label">Recruitment Date:</span>
                                        <span class="referral-date">Jan 15, 2024</span>
                                    </div>
                                    <span class="employment-history">1 year, 3 months at GigGenius</span>
                                    <span class="referral-name">John Richard Bercades</span>
                                    <span class="expertise">UI/UX Designer</span>
                                    <span class="referral-status">Active</span>
                                </div>
                            </div>
                            <div class="referral-stats">
                                <span class="commission">Commission: $250</span>
                                <button class="contact-button">Message</button>
                            </div>
                        </div>

                        <div class="referral-item">
                            <div class="referral-info">
                                <img src="{{ url_for('static', filename='img/default_profile.png') }}" alt="Profile Picture" class="referral-avatar">
                                <div class="referral-details">
                                    <div class="date-info">
                                        <span class="date-label">Recruitment Date:</span>
                                        <span class="referral-date">Jan 20, 2024</span>
                                    </div>
                                    <span class="employment-history">8 months at GigGenius</span>
                                    <span class="referral-name">Nash Esguerra</span>
                                    <span class="expertise">Frontend Developer</span>
                                    <span class="referral-status">Active</span>
                                </div>
                            </div>
                            <div class="referral-stats">
                                <span class="commission">Commission: $180</span>
                                <button class="contact-button">Message</button>
                            </div>
                        </div>

                        <div class="referral-item">
                            <div class="referral-info">
                                <img src="{{ url_for('static', filename='img/default_profile.png') }}" alt="Profile Picture" class="referral-avatar">
                                <div class="referral-details">
                                    <div class="date-info">
                                        <span class="date-label">Recruitment Date:</span>
                                        <span class="referral-date">Jan 25, 2024</span>
                                    </div>
                                    <span class="employment-history">2 weeks at GigGenius</span>
                                    <span class="referral-name">Hev Gagi</span>
                                    <span class="expertise">Backend Developer</span>
                                    <span class="referral-status pending">In Review</span>
                                </div>
                            </div>
                            <div class="referral-stats">
                                <span class="commission">Commission: Pending</span>
                                <button class="contact-button">Message</button>
                            </div>
                        </div>

                        <div class="referral-item">
                            <div class="referral-info">
                                <img src="{{ url_for('static', filename='img/default_profile.png') }}" alt="Profile Picture" class="referral-avatar">
                                <div class="referral-details">
                                    <div class="date-info">
                                        <span class="date-label">Recruitment Date:</span>
                                        <span class="referral-date">Jan 28, 2024</span>
                                    </div>
                                    <span class="employment-history">6 months at GigGenius</span>
                                    <span class="referral-name">Lesly Victoria</span>
                                    <span class="expertise">Content Writer</span>
                                    <span class="referral-status">Active</span>
                                </div>
                            </div>
                            <div class="referral-stats">
                                <span class="commission">Commission: $120</span>
                                <button class="contact-button">Message</button>
                            </div>
                        </div>

                        <div class="referral-item">
                            <div class="referral-info">
                                <img src="{{ url_for('static', filename='img/default_profile.png') }}" alt="Profile Picture" class="referral-avatar">
                                <div class="referral-details">
                                    <div class="date-info">
                                        <span class="date-label">Recruitment Date:</span>
                                        <span class="referral-date">Jan 30, 2024</span>
                                    </div>
                                    <span class="employment-history">3 weeks at GigGenius</span>
                                    <span class="referral-name">Carlo Caburnay</span>
                                    <span class="expertise">Data Scientist</span>
                                    <span class="referral-status pending">In Review</span>
                                </div>
                            </div>
                            <div class="referral-stats">
                                <span class="commission">Commission: Pending</span>
                                <button class="contact-button">Message</button>
                            </div>
                        </div>

                        <div class="referral-item">
                            <div class="referral-info">
                                <img src="{{ url_for('static', filename='img/default_profile.png') }}" alt="Profile Picture" class="referral-avatar">
                                <div class="referral-details">
                                    <div class="date-info">
                                        <span class="date-label">Recruitment Date:</span>
                                        <span class="referral-date">Feb 1, 2024</span>
                                    </div>
                                    <span class="employment-history">5 months at GigGenius</span>
                                    <span class="referral-name">Jonathan Lagazon</span>
                                    <span class="expertise">DevOps Engineer</span>
                                    <span class="referral-status">Active</span>
                                </div>
                            </div>
                            <div class="referral-stats">
                                <span class="commission">Commission: $150</span>
                                <button class="contact-button">Message</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Jobs Referred to You Section -->
                <div class="section">
                    <div class="section-header">
                        <h3 class="section-title">Jobs Referred to You</h3>
                    </div>
                    <div class="section-content">
                        <table class="offers-table">
                            <thead>
                                <tr>
                                    <th>Job Title</th>
                                    <th>Referred By</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <a href="#" class="job-link">Frontend Developer</a>
                                        <span class="company">TechCorp Solutions</span>
                                    </td>
                                    <td>John Richard Bercades</td>
                                    <td>Feb 15, 2024</td>
                                    <td><span class="status-badge open">Open</span></td>
                                    <td>
                                        <button class="action-btn">View Details</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <a href="#" class="job-link">Mobile App Developer</a>
                                        <span class="company">AppWorks Inc.</span>
                                    </td>
                                    <td>Marivient Yurag</td>
                                    <td>Feb 10, 2024</td>
                                    <td><span class="status-badge open">Open</span></td>
                                    <td>
                                        <button class="action-btn">View Details</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <a href="#" class="job-link">Backend Developer</a>
                                        <span class="company">ServerPro Solutions</span>
                                    </td>
                                    <td>Jandel Caburnay</td>
                                    <td>Feb 8, 2024</td>
                                    <td><span class="status-badge closed">Closed</span></td>
                                    <td>
                                        <button class="action-btn">View Details</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <a href="#" class="job-link">UI/UX Designer</a>
                                        <span class="company">DesignHub Creative</span>
                                    </td>
                                    <td>Nash Esguerra</td>
                                    <td>Feb 12, 2024</td>
                                    <td><span class="status-badge open">Open</span></td>
                                    <td>
                                        <button class="action-btn">View Details</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <a href="#" class="job-link">Data Analyst</a>
                                        <span class="company">DataInsights Corp</span>
                                    </td>
                                    <td>Carlo Caburnay</td>
                                    <td>Feb 14, 2024</td>
                                    <td><span class="status-badge open">Open</span></td>
                                    <td>
                                        <button class="action-btn">View Details</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Archived Tab Content -->
            <div id="archivedTab" style="display: none;">
                <!-- Archived Proposals Section -->
                <div class="section">
                    <div class="section-header">
                        <h3 class="section-title">
                            Archived Proposals <span class="count">(5)</span>
                            <i class="fas fa-info-circle info-icon"></i>
                        </h3>
                    </div>
                    <div class="section-content">
                        <div class="proposal-item">
                            <div class="proposal-info">
                                <div class="date-container">
                                    <span class="date">Feb 10, 2024</span>
                                    <span class="timestamp">5 days ago</span>
                                </div>
                                <div class="work-details">
                                    <a href="#" class="proposal-title">Web Developer for E-commerce Platform</a>
                                    <span class="proposal-budget">$35-40/hr</span>
                                </div>
                                <span class="status">Declined</span>
                            </div>
                            <button class="profile-button">View Proposal</button>
                        </div>

                        <div class="proposal-item">
                            <div class="proposal-info">
                                <div class="date-container">
                                    <span class="date">Feb 8, 2024</span>
                                    <span class="timestamp">1 week ago</span>
                                </div>
                                <div class="work-details">
                                    <a href="#" class="proposal-title">WordPress Developer - Custom Theme Development</a>
                                    <span class="proposal-budget">Fixed Price - $2,500</span>
                                </div>
                                <span class="status">Withdrawn</span>
                            </div>
                            <button class="profile-button">View Proposal</button>
                        </div>

                        <div class="proposal-item">
                            <div class="proposal-info">
                                <div class="date-container">
                                    <span class="date">Feb 5, 2024</span>
                                    <span class="timestamp">10 days ago</span>
                                </div>
                                <div class="work-details">
                                    <a href="#" class="proposal-title">Full Stack Developer - MERN Stack</a>
                                    <span class="proposal-budget">$45-50/hr</span>
                                </div>
                                <span class="status">Expired</span>
                            </div>
                            <button class="profile-button">View Proposal</button>
                        </div>

                        <div class="proposal-item">
                            <div class="proposal-info">
                                <div class="date-container">
                                    <span class="date">Feb 3, 2024</span>
                                    <span class="timestamp">12 days ago</span>
                                </div>
                                <div class="work-details">
                                    <a href="#" class="proposal-title">Mobile App Developer - React Native</a>
                                    <span class="proposal-budget">$40-50/hr</span>
                                </div>
                                <span class="status">Declined</span>
                            </div>
                            <button class="profile-button">View Proposal</button>
                        </div>

                        <div class="proposal-item">
                            <div class="proposal-info">
                                <div class="date-container">
                                    <span class="date">Feb 1, 2024</span>
                                    <span class="timestamp">2 weeks ago</span>
                                </div>
                                <div class="work-details">
                                    <a href="#" class="proposal-title">UI/UX Designer for SaaS Platform</a>
                                    <span class="proposal-budget">Fixed Price - $3,000</span>
                                </div>
                                <span class="status">Withdrawn</span>
                            </div>
                            <button class="profile-button">View Proposal</button>
                        </div>
                    </div>
                </div>

                <!-- Archived Interviews Section -->
                <div class="section">
                    <div class="section-header">
                        <h3 class="section-title">
                            Archived Interviews <span class="count">(4)</span>
                            <i class="fas fa-info-circle info-icon"></i>
                        </h3>
                    </div>
                    <div class="section-content">
                        <div class="interview-item">
                            <div class="interview-info">
                                <div class="date-container">
                                    <span class="date">Feb 12, 2024</span>
                                    <span class="timestamp">3 days ago</span>
                                </div>
                                <div class="work-details">
                                    <a href="#" class="proposal-title">Frontend Developer - React Specialist</a>
                                    <span class="proposal-budget">$40-45/hr</span>
                                </div>
                                <span class="status">Declined</span>
                            </div>
                            <div class="interview-details">
                                <span>Scheduled: Feb 12, 2024 10:00 AM</span>
                                <span>Duration: 45 minutes</span>
                            </div>
                            <button class="profile-button">View Details</button>
                        </div>

                        <div class="interview-item">
                            <div class="interview-info">
                                <div class="date-container">
                                    <span class="date">Feb 7, 2024</span>
                                    <span class="timestamp">1 week ago</span>
                                </div>
                                <div class="work-details">
                                    <a href="#" class="proposal-title">Senior Backend Developer - Python/Django</a>
                                    <span class="proposal-budget">$50-55/hr</span>
                                </div>
                                <span class="status">Missed</span>
                            </div>
                            <div class="interview-details">
                                <span>Scheduled: Feb 7, 2024 2:30 PM</span>
                                <span>Duration: 60 minutes</span>
                            </div>
                            <button class="profile-button">View Details</button>
                        </div>

                        <div class="interview-item">
                            <div class="interview-info">
                                <div class="date-container">
                                    <span class="date">Feb 5, 2024</span>
                                    <span class="timestamp">10 days ago</span>
                                </div>
                                <div class="work-details">
                                    <a href="#" class="proposal-title">Full Stack Developer - JavaScript</a>
                                    <span class="proposal-budget">$45-50/hr</span>
                                </div>
                                <span class="status">Declined</span>
                            </div>
                            <div class="interview-details">
                                <span>Scheduled: Feb 5, 2024 11:15 AM</span>
                                <span>Duration: 30 minutes</span>
                            </div>
                            <button class="profile-button">View Details</button>
                        </div>

                        <div class="interview-item">
                            <div class="interview-info">
                                <div class="date-container">
                                    <span class="date">Feb 2, 2024</span>
                                    <span class="timestamp">2 weeks ago</span>
                                </div>
                                <div class="work-details">
                                    <a href="#" class="proposal-title">Mobile App Developer - Flutter</a>
                                    <span class="proposal-budget">$40-45/hr</span>
                                </div>
                                <span class="status">Missed</span>
                            </div>
                            <div class="interview-details">
                                <span>Scheduled: Feb 2, 2024 3:00 PM</span>
                                <span>Duration: 45 minutes</span>
                            </div>
                            <button class="profile-button">View Details</button>
                        </div>
                    </div>
                </div>

                <!-- Archived Offers Section -->
                <div class="section">
                    <div class="section-header">
                        <h3 class="section-title">
                            Archived Offers <span class="count">(3)</span>
                            <i class="fas fa-info-circle info-icon"></i>
                        </h3>
                    </div>
                    <div class="section-content">
                        <div class="proposal-item">
                            <div class="proposal-info">
                                <div class="date-container">
                                    <span class="date">Feb 9, 2024</span>
                                    <span class="timestamp">6 days ago</span>
                                </div>
                                <div class="work-details">
                                    <a href="#" class="proposal-title">UX/UI Designer for Mobile App</a>
                                    <span class="proposal-budget">$35-40/hr</span>
                                </div>
                                <span class="status">Declined</span>
                            </div>
                            <button class="profile-button">View Offer</button>
                        </div>

                        <div class="proposal-item">
                            <div class="proposal-info">
                                <div class="date-container">
                                    <span class="date">Feb 6, 2024</span>
                                    <span class="timestamp">9 days ago</span>
                                </div>
                                <div class="work-details">
                                    <a href="#" class="proposal-title">Backend Developer - Node.js</a>
                                    <span class="proposal-budget">$40-45/hr</span>
                                </div>
                                <span class="status">Expired</span>
                            </div>
                            <button class="profile-button">View Offer</button>
                        </div>

                        <div class="proposal-item">
                            <div class="proposal-info">
                                <div class="date-container">
                                    <span class="date">Feb 4, 2024</span>
                                    <span class="timestamp">11 days ago</span>
                                </div>
                                <div class="work-details">
                                    <a href="#" class="proposal-title">WordPress Developer</a>
                                    <span class="proposal-budget">Fixed Price - $2,000</span>
                                </div>
                                <span class="status">Declined</span>
                            </div>
                            <button class="profile-button">View Offer</button>
                        </div>
                    </div>
                </div>
            </div>
        </main>


    </div>

    <!-- Video Modal -->
    <div id="videoModal" class="modal">
        <div class="modal-content">
            <span class="close-button">&times;</span>
            <h2>Safety Tips Video</h2>
            <video id="safetyVideo" controls width="100%">
                <source src="{{ url_for('static', filename='videos/safety_tips.mp4') }}" type="video/mp4">
                Your browser does not support the video tag.
            </video>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Tab Switching
            const tabButtons = document.querySelectorAll('.tab-button');
            const activeTab = document.getElementById('activeTab');
            const referralsTab = document.getElementById('referralsTab');
            const archivedTab = document.getElementById('archivedTab');

            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    // Remove active class from all tabs
                    tabButtons.forEach(btn => btn.classList.remove('active'));

                    // Add active class to clicked tab
                    button.classList.add('active');

                    // Hide all tabs
                    activeTab.style.display = 'none';
                    referralsTab.style.display = 'none';
                    archivedTab.style.display = 'none';

                    // Show selected tab
                    if (button.textContent.trim() === 'Active') {
                        activeTab.style.display = 'block';
                    } else if (button.textContent.trim() === 'Referrals') {
                        referralsTab.style.display = 'block';
                    } else if (button.textContent.trim() === 'Archived') {
                        archivedTab.style.display = 'block';
                    }
                });
            });

            // View More/Less functionality
            const viewMoreButtons = document.querySelectorAll('.view-more-button');

            viewMoreButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const sectionContent = this.closest('.section-content');
                    const items = sectionContent.querySelectorAll('.proposal-item');

                    if (sectionContent.classList.contains('expanded')) {
                        // Collapse
                        sectionContent.classList.remove('expanded');
                        for (let i = 2; i < items.length; i++) {
                            items[i].style.display = 'none';
                        }
                        this.textContent = 'View More';
                    } else {
                        // Expand
                        sectionContent.classList.add('expanded');
                        for (let i = 0; i < items.length; i++) {
                            items[i].style.display = 'flex';
                        }
                        this.textContent = 'View Less';
                    }
                });

                // Initialize: hide items beyond the first two
                const sectionContent = button.closest('.section-content');
                const items = sectionContent.querySelectorAll('.proposal-item');

                if (items.length > 2) {
                    for (let i = 2; i < items.length; i++) {
                        items[i].style.display = 'none';
                    }
                } else {
                    button.parentElement.style.display = 'none';
                }
            });

            // Modal functionality
            const modal = document.getElementById('videoModal');
            const closeButton = document.querySelector('.close-button');
            const watchButton = document.querySelector('.watch-button');
            const video = document.getElementById('safetyVideo');

            watchButton.addEventListener('click', () => {
                modal.style.display = 'block';
                video.play();
            });

            closeButton.addEventListener('click', () => {
                modal.style.display = 'none';
                video.pause();
                video.currentTime = 0;
            });

            window.addEventListener('click', (event) => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                    video.pause();
                    video.currentTime = 0;
                }
            });

            video.addEventListener('ended', () => {
                alert('Congratulations! You\'ve earned 8 Connects for watching the safety video.');
                modal.style.display = 'none';
                video.currentTime = 0;
            });

            // Mobile Menu Toggle
            const mobileMenuBtn = document.getElementById('mobileMenuBtn');
            const mobileMenu = document.getElementById('mobileMenu');

            if (mobileMenuBtn && mobileMenu) {
                mobileMenuBtn.addEventListener('click', () => {
                    mobileMenu.classList.toggle('active');
                });

                // Close mobile menu when clicking outside
                document.addEventListener('click', (event) => {
                    if (!mobileMenuBtn.contains(event.target) && !mobileMenu.contains(event.target)) {
                        mobileMenu.classList.remove('active');
                    }
                });
            }

            // For mobile devices, make dropdown items clickable to expand
            const dropdownTriggers = document.querySelectorAll('.dropdown-trigger');

            dropdownTriggers.forEach(trigger => {
                trigger.addEventListener('click', function(e) {
                    if (window.innerWidth < 768) {
                        e.preventDefault();
                        const dropdown = this.nextElementSibling;
                        dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
                    }
                });
            });

            // Close dropdowns when clicking outside
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.dropdown')) {
                    const dropdownMenus = document.querySelectorAll('.dropdown-menu');
                    dropdownMenus.forEach(menu => {
                        menu.style.display = 'none';
                    });
                }

                // Close profile dropdown when clicking outside
                if (!e.target.closest('.profile-dropdown')) {
                    const profileDropdown = document.querySelector('.profile-dropdown-content');
                    if (profileDropdown) {
                        profileDropdown.classList.remove('show');
                    }
                }

                // Close notification dropdown when clicking outside
                if (!e.target.closest('.notification-dropdown')) {
                    const notificationDropdown = document.getElementById('notificationDropdown');
                    if (notificationDropdown) {
                        notificationDropdown.classList.remove('active');
                    }
                }
            });

            // Profile dropdown toggle
            const profileButton = document.querySelector('.profile-button');
            const profileDropdown = document.querySelector('.profile-dropdown-content');

            if (profileButton && profileDropdown) {
                profileButton.addEventListener('click', function(e) {
                    e.stopPropagation();
                    profileDropdown.classList.toggle('show');

                    // Close notification dropdown if open
                    const notificationDropdown = document.getElementById('notificationDropdown');
                    if (notificationDropdown && notificationDropdown.classList.contains('active')) {
                        notificationDropdown.classList.remove('active');
                    }
                });
            }

            // Notification dropdown toggle
            const notificationBtn = document.getElementById('notificationBtn');
            const notificationDropdown = document.getElementById('notificationDropdown');
            const markAllReadBtn = document.getElementById('markAllRead');

            if (notificationBtn && notificationDropdown) {
                notificationBtn.addEventListener('click', function(event) {
                    event.stopPropagation();
                    notificationDropdown.classList.toggle('active');

                    // Close profile dropdown if open
                    const profileDropdownContent = document.querySelector('.profile-dropdown-content');
                    if (profileDropdownContent && profileDropdownContent.classList.contains('show')) {
                        profileDropdownContent.classList.remove('show');
                    }
                });

                // Mark all notifications as read
                if (markAllReadBtn) {
                    markAllReadBtn.addEventListener('click', function() {
                        const unreadNotifications = document.querySelectorAll('.notification-item.unread');
                        unreadNotifications.forEach(notification => {
                            notification.classList.remove('unread');
                        });

                        // Remove the notification indicator
                        const indicator = document.querySelector('.notification-indicator');
                        if (indicator) {
                            indicator.style.display = 'none';
                        }
                    });
                }

                // Mark individual notification as read when clicked
                const notificationItems = document.querySelectorAll('.notification-item');
                notificationItems.forEach(item => {
                    item.addEventListener('click', function() {
                        this.classList.remove('unread');

                        // Check if there are any unread notifications left
                        const indicator = document.querySelector('.notification-indicator');
                        if (indicator) {
                            indicator.style.display = 'none';
                        }
                    });
                });
            }
        });
    </script>

        <!-- Footer -->
        <footer>
            <div class="footer-grid">
                <div class="footer-column">
                    <h3>For Clients</h3>
                    <a href="#">How to Hire</a>
                    <a href="#">Marketplace</a>
                    <a href="#">Payroll Services</a>
                    <a href="#">Service Catalog</a>
                    <a href="#">Business Networking</a>
                    <a href="#">PH Business Loan</a>
                </div>
                <div class="footer-column">
                    <h3>For Geniuses</h3>
                    <a href="#">How It Works?</a>
                    <a href="#">Why Can't I Apply?</a>
                    <a href="#">Direct Contracts</a>
                    <a href="#">Find Mentors</a>
                    <a href="#">Mentor Application</a>
                    <a href="#">PH Health Insurance</a>
                    <a href="#">PH Life Insurance</a>
                </div>
                <div class="footer-column">
                    <h3>Resources</h3>
                    <a href="#">Help & Support</a>
                    <a href="#">News & Events</a>
                    <a href="#">Affiliate Program</a>
                </div>
                <div class="footer-column">
                    <h3>Company</h3>
                    <a href="#">About Us</a>
                    <a href="#">Contact Us</a>
                    <a href="#">Charity Projects</a>
                </div>
            </div>
            <div class="footer-bottom">
                <p>
                    Follow Us:
                    <span class="social-icons">
                        <a href="https://www.facebook.com/giggenius.io"><i class="bi bi-facebook"></i></a>
                        <a href="https://www.instagram.com/giggenius.io/"><i class="bi bi-instagram"></i></a>
                        <a href="https://twitter.com/giggenius_io"><i class="bi bi-twitter-x"></i></a>
                        <a href="#"><i class="bi bi-tiktok"></i></a>
                        <a href="https://www.youtube.com/@giggenius"><i class="bi bi-youtube"></i></a>
                        <a href="https://www.linkedin.com/company/gig-genius/"><i class="bi bi-linkedin"></i></a>
                    </span>
                </p>
                <p>©2025 GigGenius by <a href="https://genuinelysolutions.com/">Genuinely Business Solutions</a></p>
                <p>
                    <a href="#">Terms of Service</a> |
                    <a href="#">Privacy Policy</a>
                </p>
            </div>
        </footer>
</body>
</html>
