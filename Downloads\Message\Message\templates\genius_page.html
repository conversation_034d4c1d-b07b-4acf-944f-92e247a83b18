<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-store, no-cache, must-revalidate, post-check=0, pre-check=0, max-age=0">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="-1">
    <title>Genius Page</title>
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='img/logo.png') }}">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/optimized-job-cards.css') }}" rel="stylesheet">
    <style>

        :root {
            --primary-blue: #004AAD;
            --primary-pink: #CD208B;
            --yellow: #FFD700;
            --text-dark: #000000;
            --text-light: #FFFFFF;
            --text-gray: #666;
        }

        html, body {
            height: 100%;
            min-height: 100vh;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        body {
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        /* Container Styles */
        .container {
            max-width: 2000px;
            margin: auto;
            padding: auto;
        }

        /* Navbar Styles */
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 1rem;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            height: 5rem;
            position: relative;
            z-index: 1000;
            min-height: 5rem;
        }

        .navbar-left {
            display: flex;
            align-items: center;
            gap: 2rem;
            padding-left: 0;
            flex: 1;
        }

        .logo {
            display: flex;
            align-items: center;
            color: var(--primary-pink);
        }

        .logo img {
            width: 3.5rem;
            height: 3.5rem;
        }

        .logo h1 {
            font-size: 1.5rem;
            font-weight: bold;
            margin-left: 0.5rem;
            margin-right: 0.5rem;
            color: var(--primary-pink);
        }

        .logo:hover, .logo:active {
            color: var(--primary-blue);
        }

        .logo:hover h1 {
            color: var(--primary-blue);
        }

        .nav-links {
            display: flex;
            gap: 1rem;
            align-items: center;
            height: 100%;
            margin: 0;
        }

        .nav-links a {
            color: var(--primary-blue);
            text-decoration: none;
            padding: 0.5rem 1rem;
            font-size: 1rem;
            font-weight: 500;
            border-radius: 6px;
            transition: all 0.3s ease;
            position: relative;
        }
        /* Remove underline from individual nav items */
        .nav-links > a:after,
        .nav-dropbtn:after {
            display: none !important;
        }
        .nav-links > a,
        .nav-links > a:hover,
        .nav-links > a.active,
        .nav-dropbtn,
        .nav-dropbtn:hover,
        .nav-dropbtn.active,
        .nav-dropdown-content a,
        .nav-dropdown-content a:hover {
            text-decoration: none !important;
            border-bottom: none !important;
        }
        .nav-links > a:hover, .nav-links > a.active,
        .nav-dropbtn:hover, .nav-dropbtn.active {
            color: var(--primary-pink);
        }
        .nav-dropdown-content a:after {
            display: none !important;
        }
        .navbar {
            position: relative;
        }
        .nav-links > a:hover, .nav-links > a.active {
            color: var(--primary-pink);
            background-color: transparent;
        }
        .nav-dropbtn:hover, .nav-dropbtn.active {
            color: var(--primary-pink);
            background-color: transparent;
        }

        .nav-dropbtn {
            font-weight: 600;
            font-size: 1rem;
            color: var(--primary-blue);
            background: none;
            border: none;
            padding: 0.5rem 1rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            border-radius: 6px;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-dropdown {
            position: relative;
            display: inline-block;
        }

        .nav-dropbtn {
            font-weight: 600;
            font-size: 1rem;
            color: var(--primary-blue);
            background: none;
            border: none;
            padding: 0.5rem 1rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            border-radius: 6px;
            transition: all 0.3s ease;
            border-bottom: 2px solid transparent;
            position: relative;
        }

        .nav-dropbtn:hover, .nav-dropbtn.active {
            color: var(--primary-pink);
            background-color: transparent;
            border-bottom: 2px solid var(--primary-pink);
        }

        .nav-dropdown-content {
            display: none;
            position: absolute;
            background-color: #fff;
            min-width: 200px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            border-radius: 8px;
            z-index: 1001;
            top: 100%;
            left: 0;
        }

        .nav-dropdown-content a {
            color: var(--primary-blue);
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            font-size: 1rem;
        }

        .nav-dropdown-content a:hover {
            background-color: transparent;
            color: var(--primary-pink);
        }

        .nav-dropdown:hover .nav-dropdown-content {
            display: block;
        }

        /* Right section container */
        .right-section {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            padding-right: 1rem;
            justify-content: flex-end;
            height: 100%;
        }

        /* Search container */
        .search-container {
            display: flex;
            align-items: center;
            height: 100%;
        }

        .search-bar {
            display: flex;
            align-items: center;
            background: white;
            border: 2px solid var(--primary-blue);
            border-radius: 25px;
            height: 36px;
            width: 260px;
            transition: all 0.3s ease;
            position: relative;
        }

        .search-bar:hover {
            border-color: var(--primary-pink);
            box-shadow: 0 0 0 3px rgba(205, 32, 139, 0.1);
        }

        .search-bar:focus-within {
            border-color: var(--primary-pink);
            box-shadow: 0 0 0 3px rgba(205, 32, 139, 0.1);
        }

        .search-bar input {
            border: none;
            outline: none;
            padding: 0 14px;
            flex: 1;
            height: 100%;
            font-size: 0.85rem;
            background: transparent;
            color: #333;
            font-family: 'Poppins', sans-serif;
            line-height: 1;
            display: flex;
            align-items: center;
        }

        .search-bar input::placeholder {
            color: #999;
            font-size: 0.8rem;
            font-family: 'Poppins', sans-serif;
        }

        .search-bar .icon {
            color: var(--primary-blue);
            padding: 0 14px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: color 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
        }

        .search-bar:hover .icon {
            color: var(--primary-pink);
        }


        /* Auth buttons container */
        .auth-buttons {
            display: flex;
            align-items: center;
            gap: 1rem;
            height: 100%;
        }

        /* Notification icon */
        .notification-icon {
            position: relative;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 50%;
            transition: all 0.3s ease;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Notification container to properly position dropdown */
        .notification-container {
            position: relative;
            display: inline-block;
        }

        .notification-icon:hover {
            background-color: rgba(0, 74, 173, 0.1);
        }

        .notification-icon i {
            font-size: 1.4rem;
            color: var(--primary-blue);
            transition: color 0.3s ease;
        }

        .notification-icon:hover i {
            color: var(--primary-pink);
        }

        .notification-badge {
            position: absolute;
            top: 2px;
            right: 2px;
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            border-radius: 50%;
            padding: 0.2rem 0.4rem;
            font-size: 0.7rem;
            min-width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            border: 2px solid white;
            box-shadow: 0 2px 8px rgba(231, 76, 60, 0.4);
            animation: pulse 2s infinite;
            transition: all 0.3s ease;
        }

        .notification-badge.new {
            animation: bounce 0.6s ease-in-out;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-3px); }
            20%, 40%, 60%, 80% { transform: translateX(3px); }
        }

        /* Professional Notification dropdown */
        .notification-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid rgba(0, 74, 173, 0.08);
            border-radius: 16px;
            box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.08),
                0 8px 16px rgba(0, 0, 0, 0.04),
                0 0 0 1px rgba(255, 255, 255, 0.05);
            width: 380px;
            max-height: 480px;
            overflow: hidden;
            z-index: 1001;
            display: none;
            margin-top: 12px;
            backdrop-filter: blur(10px);
            animation: slideDown 0.3s ease-out;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .notification-header {
            padding: 16px 20px 12px;
            border-bottom: 1px solid rgba(0, 74, 173, 0.06);
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: 700;
            color: var(--primary-blue);
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
            position: relative;
        }

        .notification-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-blue) 0%, var(--primary-pink) 100%);
            border-radius: 16px 16px 0 0;
        }

        .notification-header-actions {
            font-size: 0.8rem;
            color: var(--primary-pink);
            cursor: pointer;
            padding: 6px 12px;
            border-radius: 8px;
            transition: all 0.2s ease;
            font-weight: 600;
            background: rgba(205, 32, 139, 0.05);
            border: 1px solid rgba(205, 32, 139, 0.1);
        }

        .notification-header-actions:hover {
            background: rgba(205, 32, 139, 0.1);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(205, 32, 139, 0.15);
        }

        .empty-notifications {
            padding: 40px 20px;
            text-align: center;
            color: #6b7280;
            background: linear-gradient(135deg, #f9fafb 0%, #ffffff 100%);
        }

        .empty-notifications i {
            font-size: 3rem;
            margin-bottom: 16px;
            opacity: 0.3;
            color: var(--primary-blue);
            background: linear-gradient(135deg, var(--primary-blue), var(--primary-pink));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .empty-notifications h3 {
            font-size: 1.1rem;
            font-weight: 600;
            color: #374151;
            margin: 0 0 8px 0;
        }

        .empty-notifications p {
            font-size: 0.9rem;
            color: #6b7280;
            margin: 0;
            line-height: 1.5;
        }

        /* Professional Notification items */
        .notification-item {
            padding: 12px 20px;
            border-bottom: 1px solid rgba(0, 74, 173, 0.04);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
        }

        .notification-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-pink) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .notification-item:hover {
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
            transform: translateX(2px);
            box-shadow: 0 4px 12px rgba(0, 74, 173, 0.08);
        }

        .notification-item:hover::before {
            opacity: 1;
        }

        .notification-item:last-child {
            border-bottom: none;
            border-radius: 0 0 16px 16px;
        }

        .notification-item:first-of-type {
            border-radius: 0;
        }

        .notification-content {
            display: flex;
            flex-direction: column;
            gap: 0px;
        }

        .notification-header {
            display: flex;
            align-items: flex-start;
            gap: 10px;
            margin-bottom: 0px;
        }

        .notification-header i {
            font-size: 1.1rem;
            margin-top: 2px;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }

        .notification-title {
            font-weight: 600;
            font-size: 0.9rem;
            color: var(--primary-blue);
            line-height: 1.4;
            flex: 1;
            letter-spacing: -0.01em;
        }

        .notification-text {
            font-size: 0.85rem;
            color: #1f2937;
            margin: 0px;
            line-height: 1.3;
            font-weight: 600;
            background: linear-gradient(135deg, var(--primary-blue) 0%, #1f2937 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .notification-time {
            font-size: 0.7rem;
            color: #6b7280;
            margin-top: 0px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .notification-time::before {
            content: '•';
            color: var(--primary-pink);
            font-weight: bold;
        }

        /* Professional scrollbar for notifications */
        .notification-dropdown {
            overflow-y: auto;
        }

        .notification-dropdown::-webkit-scrollbar {
            width: 6px;
        }

        .notification-dropdown::-webkit-scrollbar-track {
            background: rgba(0, 74, 173, 0.02);
            border-radius: 3px;
        }

        .notification-dropdown::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-pink) 100%);
            border-radius: 3px;
            transition: all 0.3s ease;
        }

        .notification-dropdown::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, var(--primary-pink) 0%, var(--primary-blue) 100%);
            transform: scale(1.1);
        }

        /* Notification list container */
        #notification-list {
            max-height: 400px;
            overflow-y: auto;
        }



        /* Profile button */
        .profile-button {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            cursor: pointer;
            border: 2px solid var(--primary-blue);
            transition: all 0.3s ease;
            position: relative;
        }

        .profile-button:hover {
            transform: scale(1.05);
            border-color: var(--primary-pink);
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .profile-button:active {
            transform: scale(0.95);
        }

        .profile-dropdown.active .profile-button {
            border-color: var(--primary-pink);
            box-shadow: 0 0 0 3px rgba(205, 32, 139, 0.1);
        }

        .profile-button img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
        }

        /* Profile dropdown */
        .profile-dropdown {
            position: relative;
            display: inline-block;
        }

        .profile-dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            top: 50px;
            background-color: #fff;
            min-width: 200px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            border-radius: 8px;
            z-index: 1001;
            border: 1px solid rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .profile-dropdown-content a {
            color: var(--primary-blue);
            padding: 12px 16px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 0.9rem;
            transition: all 0.2s ease;
        }

        .profile-dropdown-content a i {
            width: 16px;
            text-align: center;
        }

        .profile-dropdown-content a:hover,
        .profile-dropdown-content a:focus {
            background-color: #f9f9f9;
            color: var(--primary-pink);
            outline: none;
        }

        .dropdown-divider {
            height: 1px;
            background-color: #eee;
            margin: 8px 0;
        }

        .logout-option {
            color: #dc3545 !important;
        }

        .logout-option:hover {
            background-color: #fff5f5 !important;
            color: #dc3545 !important;
        }

        /* Show dropdown on click */
        .profile-dropdown.active .profile-dropdown-content {
            display: block;
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }


        
        /* Mobile Responsive Styles */
        @media (max-width: 992px) {
            .nav-links {
                display: none;
            }

            .mobile-menu-btn {
                display: flex;
                position: absolute;
                left: 0.4rem;
                top: 50%;
                transform: translateY(-50%);
                z-index: 1001;
                background: rgba(255, 255, 255, 0.9);
                border: 1px solid rgba(0, 74, 173, 0.1);
                padding: 0.4rem;
                border-radius: 8px;
                transition: all 0.3s ease;
                cursor: pointer;
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
                width: 32px;
                height: 32px;
            }

            .navbar-left {
                padding-left: 2.2rem;
            }

            .logo img {
                width: 2.4rem;
                height: 2.4rem;
                border-radius: 50%;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                flex-shrink: 0;
            }

            .logo h1 {
                font-size: 1.1rem;
                margin: 0;
                white-space: nowrap;
                font-weight: 600;
                color: var(--primary-pink);
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .search-bar {
                width: 200px;
                height: 36px;
            }

            .search-bar input {
                font-size: 0.85rem;
                padding: 0 12px;
            }

            .search-bar input::placeholder {
                font-size: 0.8rem;
            }

            .search-bar .icon {
                padding: 0 12px;
            }

            .profile-button {
                width: 36px;
                height: 36px;
            }

            .notification-icon {
                width: 36px;
                height: 36px;
            }
        }

        @media (max-width: 768px) {
            .navbar {
                height: 4rem;
                padding: 0 0.8rem;
            }

            .navbar-left {
                padding-left: 2.2rem;
            }

            .logo h1 {
                font-size: 1.2rem;
            }

            .search-bar {
                width: 160px;
                height: 34px;
            }

            .search-bar input {
                font-size: 0.8rem;
                padding: 0 10px;
            }

            .search-bar .icon {
                padding: 0 10px;
            }

            .footer-content {
                grid-template-columns: repeat(2, 1fr);
            }

            .footer-bottom {
                flex-direction: column;
                text-align: center;
            }

            .footer-links {
                flex-direction: column;
                gap: 0.5rem;
            }
        }

        @media (max-width: 576px) {
            .navbar {
                height: 3.8rem;
                padding: 0 0.6rem;
            }

            .navbar-left {
                padding-left: 2rem;
            }

            .mobile-menu-btn {
                left: 0.3rem;
                padding: 0.2rem;
            }

            .logo img {
                width: 2.8rem;
                height: 2.8rem;
            }

            .logo h1 {
                font-size: 1.1rem;
            }

            .search-bar {
                width: 130px;
                height: 32px;
                font-size: 0.85rem;
            }

            .search-bar input {
                font-size: 0.75rem;
                padding: 0 8px;
            }

            .search-bar input::placeholder {
                font-size: 0.7rem;
            }

            .search-bar .icon {
                padding: 0 8px;
            }

            .profile-button {
                width: 32px;
                height: 32px;
            }

            .profile-button img {
                width: 34px;
                height: 34px;
                border: 2px solid rgba(0, 74, 173, 0.1);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
                transition: all 0.3s ease;
            }
        }

        @media (max-width: 480px) {
            .navbar {
                height: 3.5rem;
                padding: 0 0.5rem;
            }

            .navbar-left {
                padding-left: 1.8rem;
            }

            .mobile-menu-btn {
                left: 0.2rem;
                padding: 0.15rem;
            }

            .right-section {
                padding-right: 0.2rem;
                gap: 0.6rem;
                justify-content: flex-end;
            }

            .logo img {
                width: 2.5rem;
                height: 2.5rem;
            }

            .logo h1 {
                font-size: 1rem;
                margin-left: 0.2rem;
                margin-right: 0.2rem;
            }

            .search-container {
                max-width: 100px;
                margin-right: 0.3rem;
            }

            .search-bar {
                width: 110px;
                height: 30px;
                font-size: 0.8rem;
            }

            .search-bar input {
                font-size: 0.7rem;
                padding: 0 6px;
            }

            .search-bar input::placeholder {
                font-size: 0.65rem;
            }

            .search-bar .icon {
                font-size: 0.8rem;
                padding: 0 6px;
            }

            .profile-button {
                width: 30px;
                height: 30px;
            }

            .profile-button img {
                width: 26px;
                height: 26px;
            }

            .notification-icon {
                width: 30px;
                height: 30px;
            }

            .notification-dropdown {
                width: 340px;
                right: -100px;
                font-size: 0.85rem;
                max-height: 400px;
                box-shadow:
                    0 15px 30px rgba(0, 0, 0, 0.1),
                    0 5px 12px rgba(0, 0, 0, 0.05);
            }

            .notification-item {
                padding: 10px 16px;
            }

            .notification-header {
                padding: 12px 16px 8px;
            }

            .profile-dropdown-content {
                right: -30px;
                width: 180px;
                font-size: 0.85rem;
            }
        }

        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            cursor: pointer;
            width: 32px;
            height: 32px;
            border-radius: 6px;
            align-items: center;
            justify-content: center;
            color: var(--primary-blue);
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }

        .mobile-menu-btn:hover {
            color: var(--primary-pink);
            background-color: rgba(0, 74, 173, 0.05);
        }

        .mobile-menu {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            background: rgba(0, 0, 0, 0.5);
            z-index: 2000;
        }

        .mobile-menu-content {
            position: absolute;
            top: 0;
            right: 0;
            width: 280px;
            height: 100vh;
            background: white;
            padding: 1rem;
            overflow-y: auto;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }

        .mobile-menu.active .mobile-menu-content {
            transform: translateX(0);
        }

        .mobile-menu-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-bottom: 1rem;
            border-bottom: 1px solid #e5e7eb;
            margin-bottom: 1rem;
        }

        .mobile-close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--primary-blue);
            cursor: pointer;
        }

        .mobile-menu a {
            display: block;
            padding: 0.75rem 1rem;
            color: var(--primary-blue);
            font-size: 0.9rem;
            border-bottom: 1px solid #f3f4f6;
            text-decoration: none;
            transition: all 0.2s ease;
        }

        .mobile-menu a:last-child {
            border-bottom: none;
        }

        .mobile-menu a:hover {
            background-color: rgba(0, 74, 173, 0.05);
            color: var(--primary-pink);
        }

        .mobile-profile-actions {
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid #e5e7eb;
        }

        .mobile-profile-link {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            color: var(--primary-blue);
            text-decoration: none;
            transition: all 0.2s ease;
        }

        .mobile-profile-link:hover {
            background-color: rgba(0, 74, 173, 0.05);
            color: var(--primary-pink);
        }

        .mobile-profile-link.logout {
            color: #dc3545 !important;
        }

        .mobile-profile-link.logout:hover {
            background-color: rgba(220, 53, 69, 0.05) !important;
        }

        /* Main Content */
        .main-content {
            padding: 1.5rem 1rem 3rem;
        }

        /* Profile Section */
        .profile-section {
            padding: 2rem 0;
            position: relative;
        }

        .profile-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxkZWZzPjxwYXR0ZXJuIGlkPSJwYXR0ZXJuIiB4PSIwIiB5PSIwIiB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHBhdHRlcm5Vbml0cz0idXNlclNwYWNlT25Vc2UiIHBhdHRlcm5UcmFuc2Zvcm09InJvdGF0ZSgzMCkiPjxyZWN0IHg9IjAiIHk9IjAiIHdpZHRoPSIyIiBoZWlnaHQ9IjIiIGZpbGw9IiMwMDRBQUQiIG9wYWNpdHk9IjAuMDMiLz48L3BhdHRlcm4+PC9kZWZzPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjcGF0dGVybikiLz48L3N2Zz4=');
            opacity: 0.5;
            z-index: -1;
        }

        .profile-card {
            background: linear-gradient(to bottom right, #ffffff, #f8f9ff);
            border-radius: 16px;
            border: 1px solid rgba(0, 74, 173, 0.1);
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.03);
            position: relative;
            transition: box-shadow 0.3s, transform 0.3s;
        }
        .profile-card:hover {
            box-shadow: 0 16px 32px rgba(0, 0, 0, 0.12);
            transform: translateY(-2px);
        }
        .profile-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 0;
            background: linear-gradient(90deg, #004AAD, #CD208B);
            opacity: 0;
            transition: height 0.3s, opacity 0.3s;
        }
        .profile-card:hover::before {
            height: 8px;
            opacity: 1;
        }

        /* Removed hover effect */

        .profile-content {
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 1.5rem;
            padding: 1.5rem;
            position: relative;
        }

        .profile-info {
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 1.5rem;
            width: 100%;
        }

        .profile-avatar-container {
            position: relative;
            flex-shrink: 0;
        }

        .profile-avatar {
            width: 110px;
            height: 110px;
            border-radius: 50%;
            border: 2px solid rgba(0, 74, 173, 0.1);
            overflow: hidden;
            flex-shrink: 0;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
            /* Removed transition */
            position: relative;
            z-index: 1;
            max-width: 110px;
            max-height: 110px;
        }

        .profile-avatar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            box-shadow: inset 0 0 0 3px rgba(205, 32, 139, 0.1);
            z-index: 1;
        }

        /* Removed hover effect */

        .profile-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
            /* Removed transition */
            max-width: 110px;
            max-height: 110px;
        }

        .status-indicator {
            position: absolute;
            bottom: 8px;
            right: 8px;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            border: 2px solid white;
            z-index: 2;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .status-indicator.online {
            background-color: #2ecc71;
        }

        .status-indicator.away {
            background-color: #f39c12;
        }

        .status-indicator.offline {
            background-color: #e74c3c;
        }

        .availability-badge {
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(90deg, #2ecc71, #27ae60);
            color: white;
            padding: 0.3rem 0.9rem;
            border-radius: 50px;
            font-size: 0.8rem;
            font-weight: 600;
            box-shadow: 0 3px 8px rgba(46, 204, 113, 0.3);
            z-index: 2;
            white-space: nowrap;
            display: flex;
            align-items: center;
            gap: 0.3rem;
            /* Removed transition */
        }

        /* Removed hover effect */

        .profile-details {
            flex-grow: 1;
            position: relative;
        }

        .profile-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .profile-name {
            font-size: 1.4rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            position: relative;
            display: inline-block;
        }

        .profile-name::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 40px;
            height: 3px;
            background: linear-gradient(90deg, #004AAD, #CD208B);
            border-radius: 3px;
            opacity: 0.7;
        }

        .profile-name span {
            background: linear-gradient(90deg, #CD208B, #e83a9c);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            font-weight: 700;
        }

        .profile-rating {
            display: flex;
            align-items: center;
            gap: 0.2rem;
            background-color: rgba(0, 74, 173, 0.05);
            padding: 0.5rem 0.8rem;
            border-radius: 50px;
            /* Removed transition */
        }

        /* Removed hover effect */

        .profile-rating i {
            color: #f1c40f;
            font-size: 0.9rem;
        }

        .profile-rating span {
            margin-left: 0.3rem;
            font-weight: 600;
            color: #555;
            font-size: 0.9rem;
        }

        .profile-meta {
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 1.5rem;
            margin-top: 1rem;
            color: #555;
            font-size: 0.95rem;
            width: auto;
        }

        .profile-meta-item {
            display: flex;
            align-items: center;
            gap: 0.4rem;
            padding: 0.35rem 0.75rem;
            background-color: rgba(0, 74, 173, 0.05);
            border-radius: 50px;
            /* Removed transition */
        }

        /* Removed hover effect */

        .profile-meta-item i {
            color: #004AAD;
            font-size: 1rem;
        }

        .profile-actions {
            display: flex;
            flex-wrap: wrap;
            gap: 0.75rem;
            margin-top: 1.5rem;
        }

        .profile-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.4rem;
            padding: 0.4rem 0.9rem;
            font-size: 0.85rem;
            font-weight: 500;
            border-radius: 50px;
            cursor: pointer;
            /* Removed transition */
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }

        .profile-btn i {
            font-size: 0.9rem;
            /* Removed transition */
        }

        .btn-outline {
            background-color: white;
            border: 1px solid #e0e0e0;
            color: #555;
        }

        /* Removed hover effects */

        .btn-outline.active {
            background: linear-gradient(90deg, #004AAD, #0066e8);
            color: white;
            border: none;
            box-shadow: 0 5px 15px rgba(0, 74, 173, 0.2);
        }

        /* Removed hover effect */

        /* Stats Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1.25rem;
            margin-top: 1.5rem;
            position: relative;
        }

        .stat-card {
            min-width: 0;
            max-width: 100%;
        }

        @media (max-width: 1024px) {
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        @media (max-width: 600px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }

        .stat-card {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 1.75rem 1.5rem;
            text-align: center;
            background: linear-gradient(to bottom right, #ffffff, #f8f9ff);
            border-radius: 16px;
            border: 1px solid rgba(0, 74, 173, 0.1);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.03);
            transition: box-shadow 0.3s, transform 0.3s;
            position: relative;
            overflow: hidden;
        }
        .stat-card:hover {
            box-shadow: 0 16px 32px rgba(0, 0, 0, 0.12);
            transform: translateY(-2px);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 0;
            background: linear-gradient(90deg, #004AAD, #CD208B);
            opacity: 0;
            transition: height 0.3s, opacity 0.3s;
        }
        .stat-card:hover::before {
            height: 8px;
            opacity: 1;
        }

        /* Removed hover effects */

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            background: linear-gradient(90deg, #004AAD, #CD208B);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            color: transparent;
            margin-bottom: 0.5rem;
            position: relative;
            display: inline-block;
        }

        .stat-value::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 2px;
            background: linear-gradient(90deg, #004AAD, #CD208B);
            border-radius: 2px;
            opacity: 0.5;
        }

        .stat-label {
            font-size: 0.95rem;
            color: #555;
            font-weight: 500;
            margin-top: 0.5rem;
            /* Removed transition */
        }

        /* Removed hover effect */

        .stat-icon {
            position: absolute;
            top: 15px;
            right: 15px;
            font-size: 1.2rem;
            color: rgba(0, 74, 173, 0.15);
            /* Removed transition */
        }

        /* Removed hover effect */

        /* Introduction Section */
        .introduction-section {
            margin-top: 2rem;
            padding: 2rem;
            background: linear-gradient(to bottom right, #ffffff, #f8f9ff);
            border-radius: 16px;
            border: 1px solid rgba(0, 74, 173, 0.1);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.03);
            position: relative;
            overflow: hidden;
            transition: box-shadow 0.35s, transform 0.35s, background 0.35s;
        }
        .introduction-section:hover {
            box-shadow: 0 16px 32px rgba(0, 0, 0, 0.12);
            transform: translateY(-2px);
            background: linear-gradient(to bottom right, #ffffff, #f8f9ff);
        }

        .introduction-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 0;
            background: linear-gradient(90deg, #004AAD, #CD208B);
            opacity: 0;
            transition: height 0.3s, opacity 0.3s;
        }
        .introduction-section:hover::before {
            height: 8px;
            opacity: 1;
        }

        /* Removed hover effect */

        .introduction-section h3 {
            font-size: 1.35rem;
            font-weight: 600;
            margin-bottom: 1.25rem;
            color: #333;
            position: relative;
            padding-left: 15px;
        }

        .introduction-section h3::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 6px;
            height: 20px;
            background: linear-gradient(to bottom, #004AAD, #CD208B);
            border-radius: 3px;
        }

        .text-wrapper {
            display: flex;
            align-items: flex-end;
            position: relative;
        }

        .truncated-text {
            display: -webkit-box;
            -webkit-line-clamp: 3;
            line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
            margin: 0;
            line-height: 1.7;
            color: #555;
            font-size: 1rem;
            background-clip: text;
            -webkit-background-clip: text;
        }

        .show-more-btn {
            color: #004AAD;
            font-weight: 600;
            white-space: nowrap;
            margin-left: 0.5rem;
            padding: 0.25rem 0.75rem;
            background-color: rgba(0, 74, 173, 0.08);
            border-radius: 50px;
            transition: all 0.2s ease;
        }

        .show-more-btn:hover {
            background-color: rgba(0, 74, 173, 0.15);
            transform: translateY(-2px);
        }

        /* Quote Slider */
        .quote-slider {
            margin: 2.5rem 0;
            padding: 3rem 2rem;
            background: linear-gradient(135deg, rgba(0, 74, 173, 0.03) 0%, rgba(205, 32, 139, 0.03) 100%);
            border-radius: 16px;
            text-align: center;
            position: relative;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(0, 74, 173, 0.08);
        }

        .quote-slider::before {
            content: '"';
            position: absolute;
            top: 20px;
            left: 30px;
            font-size: 120px;
            font-family: Georgia, serif;
            color: rgba(0, 74, 173, 0.07);
            line-height: 1;
        }

        .quote-slider::after {
            content: '"';
            position: absolute;
            bottom: 20px;
            right: 30px;
            font-size: 120px;
            font-family: Georgia, serif;
            color: rgba(205, 32, 139, 0.07);
            line-height: 1;
        }

        .quote-navigation {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 3rem;
            margin-top: 2rem;
        }

        .quote-arrow {
            font-size: 1.2rem;
            color: #004AAD;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            width: 45px;
            height: 45px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: white;
            border-radius: 50%;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(0, 74, 173, 0.1);
        }

        .quote-arrow:hover {
            transform: scale(1.1) translateY(-3px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
            color: #CD208B;
        }

        .quote-arrow:active {
            transform: scale(0.95);
        }

        .quote {
            font-size: 1.8rem;
            font-weight: 600;
            line-height: 1.5;
            transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            max-width: 800px;
            margin: 0 auto;
            position: relative;
            padding: 0 2rem;
        }

        .main-text {
            display: block;
            background: linear-gradient(90deg, #004AAD, #0066e8);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1rem;
        }

        .sub-text {
            display: block;
            background: linear-gradient(90deg, #CD208B, #e83a9c);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-top: 1rem;
            font-size: 1.5rem;
        }

        .quote.fade {
            opacity: 0;
            transform: translateY(20px);
        }

        .quote-dots {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            margin-top: 2rem;
        }

        .quote-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: rgba(0, 74, 173, 0.2);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .quote-dot.active {
            background-color: #004AAD;
            transform: scale(1.3);
        }

        /* Dashboard Layout */
        .dashboard-content {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }
        
        /* Job Cards Grid Layout */
        .job-cards-container {
            display: flex;
            flex-wrap: wrap;
            gap: 1.5rem;
            width: 100%;
        }
        
        .job-card {
            flex: 0 0 calc(33.333% - 1rem);
            display: flex;
            flex-direction: column;
            height: 450px;
            overflow: hidden;
            position: relative;
        }
        
        .job-description {
            flex: 0 0 120px;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .job-meta {
            flex: 0 0 auto;
            overflow: hidden;
        }
        
        .job-footer {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 60px;
        }

        /* Filters Sidebar */
        .filters {
            background-color: white;
            border-radius: 16px;
            border: 1px solid #eaedf2;
            padding: 1.25rem 1.75rem 1.75rem 1.75rem;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.05);
            position: relative;
            overflow: hidden;
            margin-top: 0;
            font-size: 0.95rem;
        }
        .filters label,
        .filters .custom-select__trigger,
        .filters .custom-option,
        .filters .option-group-label,
        .filters .filter-input,
        .filters .filter-select {
            font-size: 0.9rem !important;
        }

        .filters::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #004AAD, #CD208B);
            opacity: 0.7;
        }

        .filters-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            position: relative;
        }

        .filters-header h2 {
            font-size: 1.35rem;
            font-weight: 600;
            color: #333;
            position: relative;
            padding-left: 12px;
        }

        .filters-header h2::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 20px;
            background: linear-gradient(to bottom, #004AAD, #CD208B);
            border-radius: 4px;
        }

        .clear-filters-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            background-color: #f5f5f5;
            border: none;
            color: #666;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-weight: 500;
        }

        .clear-filters-btn:hover {
            background-color: #e8e8e8;
            color: #333;
            transform: translateY(-2px);
        }

        .clear-filters-btn i {
            font-size: 0.8rem;
        }

        .filter-group {
            margin-bottom: 1.5rem;
            position: relative;
        }

        .filter-group label {
            display: block;
            font-size: 0.9rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
            color: #555;
            position: relative;
            padding-left: 8px;
        }

        .filter-group label::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 14px;
            background-color: #004AAD;
            border-radius: 3px;
            opacity: 0.7;
        }

        .filter-select, .filter-input {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            font-size: 0.9rem;
            color: #333;
            background-color: #f9f9f9;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
        }

        /* Custom select styling for category filter */
        .custom-select-wrapper {
            position: relative;
            user-select: none;
            width: 100%;
        }

        .custom-select {
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .custom-select__trigger {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0.75rem 1rem;
            font-size: 0.9rem;
            color: #333;
            background-color: #f9f9f9;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
        }

        .custom-select__trigger:hover {
            border-color: #bbb;
            background-color: #f5f5f5;
        }

        .custom-options {
            position: absolute;
            display: block;
            top: 100%;
            left: 0;
            right: 0;
            border: 1px solid #e0e0e0;
            border-top: 0;
            background: #fff;
            opacity: 0;
            visibility: hidden;
            pointer-events: none;
            z-index: 2;
            border-radius: 0 0 8px 8px;
            box-shadow: 0 5px 10px rgba(0,0,0,0.1);
            max-height: 300px;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: #bbb #f1f1f1;
        }

        .custom-select.open .custom-options {
            opacity: 1;
            visibility: visible;
            pointer-events: all;
            animation: fadeIn 0.3s ease;
        }

        .custom-option {
            position: relative;
            display: block;
            padding: 10px 15px;
            font-size: 0.9rem;
            color: #333;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .custom-option:hover {
            background-color: #f0f7ff;
        }

        .custom-option.selected {
            color: #004AAD;
            background-color: #f0f7ff;
            font-weight: 500;
        }

        .option-group-label {
            display: block;
            padding: 10px 15px;
            font-size: 0.85rem;
            font-weight: 600;
            color: #004AAD;
            background-color: #f5f7fa;
            border-top: 1px solid #e0e0e0;
            border-bottom: 1px solid #e0e0e0;
            pointer-events: none;
        }

        .arrow {
            position: relative;
            height: 12px;
            width: 12px;
            display: inline-block;
            transition: all 0.3s ease;
        }

        .arrow::before, .arrow::after {
            content: "";
            position: absolute;
            bottom: 0;
            width: 0.15rem;
            height: 100%;
            transition: all 0.3s ease;
            background-color: #666;
        }

        .arrow::before {
            left: -2px;
            transform: rotate(45deg);
        }

        .arrow::after {
            left: 2px;
            transform: rotate(-45deg);
        }

        .open .arrow::before {
            left: -2px;
            transform: rotate(-45deg);
        }

        .open .arrow::after {
            left: 2px;
            transform: rotate(45deg);
        }

        /* Style the scrollbar for webkit browsers */
        .custom-options::-webkit-scrollbar {
            width: 8px;
        }

        .custom-options::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .custom-options::-webkit-scrollbar-thumb {
            background: #bbb;
            border-radius: 4px;
        }

        .custom-options::-webkit-scrollbar-thumb:hover {
            background: #999;
        }

        .filter-select:focus, .filter-input:focus {
            outline: none;
            border-color: #004AAD;
            box-shadow: 0 0 0 3px rgba(0, 74, 173, 0.1);
            background-color: #fff;
        }

        .filter-select:hover, .filter-input:hover {
            border-color: #bbb;
            background-color: #f5f5f5;
        }

        .filter-section {
            margin-bottom: 1.5rem;
            border-top: 1px solid #eaedf2;
            padding-top: 1.5rem;
            position: relative;
        }

        .filter-section::before {
            content: '';
            position: absolute;
            top: -1px;
            left: 0;
            width: 50px;
            height: 1px;
            background-color: #004AAD;
        }

        .filter-section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            padding: 0.5rem 0;
            transition: all 0.2s ease;
        }

        .filter-section-header:hover h3 {
            color: #004AAD;
        }

        .filter-section-header h3 {
            font-size: 1.1rem;
            font-weight: 600;
            color: #444;
            transition: color 0.2s ease;
        }

        .filter-section-header i {
            color: #004AAD;
            transition: transform 0.3s ease;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(0, 74, 173, 0.08);
            border-radius: 50%;
        }

        .filter-section-header.active i {
            transform: rotate(180deg);
            background-color: rgba(0, 74, 173, 0.15);
        }

        .filter-section-content {
            display: none;
            padding-top: 1rem;
            animation: fadeIn 0.3s ease;
        }

        .filter-section-content.show {
            display: block;
        }

        .toggle-container {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
            padding: 0.75rem;
            background-color: #f5f7fa;
            border-radius: 8px;
            transition: background-color 0.2s ease;
        }

        .toggle-container:hover {
            background-color: #eef2f7;
        }

        .toggle-label {
            font-size: 0.9rem;
            color: #555;
            font-weight: 500;
        }

        .toggle-label span {
            font-weight: 600;
            color: #e74c3c;
            transition: color 0.2s ease;
        }

        .toggle-label span.on {
            color: #2ecc71;
        }

        .toggle-switch {
            position: relative;
            width: 44px;
            height: 22px;
            background-color: #e74c3c;
            border-radius: 22px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
        }

        .toggle-switch.on {
            background-color: #2ecc71;
        }

        .toggle-switch .slider {
            position: absolute;
            width: 18px;
            height: 18px;
            background-color: white;
            border-radius: 50%;
            top: 2px;
            left: 2px;
            transition: all 0.3s ease;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }

        .toggle-switch.on .slider {
            left: calc(100% - 20px);
        }

        .visibility-pill {
            display: inline-block;
            padding: 0.5rem 1rem;
            background-color: #f0f7ff;
            color: #004AAD;
            border-radius: 50px;
            font-size: 0.85rem;
            font-weight: 500;
            box-shadow: 0 2px 4px rgba(0, 74, 173, 0.1);
            transition: all 0.2s ease;
            border: 1px solid rgba(0, 74, 173, 0.1);
        }

        .visibility-pill:hover {
            background-color: #e0efff;
            box-shadow: 0 3px 6px rgba(0, 74, 173, 0.15);
            transform: translateY(-1px);
        }

        .my-proposal-link {
            display: block;
            padding: 0.9rem 1.2rem;
            background-color: #f9f9f9;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            color: #333;
            font-size: 0.95rem;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
            text-decoration: none;
        }

        .my-proposal-link:hover {
            border-color: #004AAD;
            background-color: #f0f7ff;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
        }

        .my-proposal-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .my-proposal-item span {
            font-weight: 500;
        }

        .my-proposal-item i {
            color: #004AAD;
            font-size: 0.8rem;
            transition: transform 0.2s ease;
        }

        .my-proposal-link:hover .my-proposal-item i {
            transform: translateX(3px);
        }

        /* Search and Jobs */
        .jobs-container {
            flex: 1;
        }

        .search-container {
            position: relative;
            margin-bottom: 1.5rem;
        }

        .job-search-bar {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
        }

        .no-results {
            text-align: center;
            padding: 3rem 1rem;
            color: #6b7280;
            background-color: white;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }

        .no-jobs {
            text-align: center;
            padding: 4rem 2rem;
            background: linear-gradient(to bottom, #ffffff, #f8f9fa);
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
            grid-column: 1 / -1;
            border: 1px dashed rgba(0, 74, 173, 0.2);
            position: relative;
            overflow: hidden;
        }

        .no-jobs::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #004AAD, #CD208B);
            opacity: 0.5;
        }

        .no-jobs h2 {
            color: #004AAD;
            margin-bottom: 1rem;
            font-size: 1.75rem;
            font-weight: 700;
        }

        .no-jobs p {
            color: #6b7280;
            margin-bottom: 2rem;
            font-size: 1.1rem;
            max-width: 500px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Job Cards - Styles moved to optimized-job-cards.css */

        /* Job card styles moved to optimized-job-cards.css */

        /* Header and avatar styles moved to optimized-job-cards.css */

        /* Client info and job title styles moved to optimized-job-cards.css */

        /* Job body and description styles moved to optimized-job-cards.css */

        /* Job meta styles moved to optimized-job-cards.css */

        /* Job tags and skills styles moved to optimized-job-cards.css */

        /* Job date styles moved to optimized-job-cards.css */

        /* Simplified Pagination Styles */
        .pagination-container {
            margin-top: 1.5rem;
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
        }

        .pagination {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .pagination-btn {
            display: inline-block;
            padding: 0.5rem 1rem;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            color: #004AAD;
            font-weight: 500;
            font-size: 0.9rem;
            cursor: pointer;
            text-decoration: none;
        }

        .pagination-btn:hover {
            background-color: #e9ecef;
        }

        .pagination-btn.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            color: #6c757d;
        }

        .pagination-numbers {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            flex-wrap: wrap;
        }

        .pagination-number {
            display: inline-block;
            min-width: 32px;
            height: 32px;
            line-height: 32px;
            text-align: center;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            color: #333;
            font-weight: 500;
            text-decoration: none;
        }

        .pagination-number:hover {
            background-color: #e9ecef;
        }

        .pagination-number.active {
            background-color: #004AAD;
            color: white;
            border-color: #004AAD;
        }

        .pagination-ellipsis {
            display: inline-block;
            min-width: 32px;
            height: 32px;
            line-height: 32px;
            text-align: center;
            color: #6c757d;
        }

        .pagination-info {
            margin-top: 0.5rem;
            color: #6c757d;
            font-size: 0.85rem;
            text-align: center;
        }

        /* Simplified Job Footer */
        .job-footer {
            padding: 1rem;
            border-top: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #f8f9fa;
        }

        /* Simplified Job Price Container */
        .job-price-container {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .budget-label {
            font-size: 0.8rem;
            color: #6b7280;
            font-weight: 500;
        }

        .job-price {
            font-size: 1.1rem;
            font-weight: 700;
            color: #004AAD;
        }

        .budget-type {
            font-size: 0.8rem;
            color: #6b7280;
            font-weight: 500;
        }

        /* Simplified View Button */
        .view-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.5rem 1rem;
            background-color: #004AAD;
            color: white;
            font-weight: 600;
            border-radius: 4px;
            text-decoration: none;
            border: none;
            cursor: pointer;
            font-size: 0.9rem;
        }

        .view-btn:hover {
            background-color: #003b8a;
        }

        /* Simplified Pagination (Alternative) */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 1.5rem;
            gap: 0.25rem;
        }

        .page-item {
            display: inline-block;
            width: 32px;
            height: 32px;
            line-height: 32px;
            text-align: center;
            border: 1px solid #e5e7eb;
            border-radius: 4px;
            font-size: 0.875rem;
            color: #6b7280;
            cursor: pointer;
        }

        .page-item:hover {
            border-color: #004AAD;
            color: #004AAD;
        }

        .page-item.active {
            background-color: #004AAD;
            border-color: #004AAD;
            color: white;
        }

        .page-arrow {
            color: #6b7280;
        }

        .page-arrow:hover {
            color: #004AAD;
        }

        /* Content Sections */
        .content-section {
            display: none;
        }

        .content-section.active {
            display: block;
        }

        /* My Applications Styles */
        .jobs-header {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .jobs-title {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .jobs-filters {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .filter-select, .filter-input {
            padding: 0.625rem 1rem;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 0.875rem;
            width: 100%;
        }

        .jobs-grid {
            display: grid;
            gap: 1.5rem;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .status-pending {
            background-color: #fff7ed;
            color: #c2410c;
            border: 1px solid #fdba74;
        }

        .status-accepted,
        .status-accept {
            background-color: #ecfdf5;
            color: #047857;
            border: 1px solid #6ee7b7;
            box-shadow: 0 2px 6px rgba(16, 185, 129, 0.15);
            font-weight: 600;
        }

        .status-rejected {
            background-color: #fef2f2;
            color: #b91c1c;
            border: 1px solid #fca5a5;
        }

        .status-review {
            background-color: #eff6ff;
            color: #1d4ed8;
            border: 1px solid #93c5fd;
        }

        .save-btn {
            display: flex;
            align-items: center;
            gap: 0.375rem;
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
            background-color: transparent;
            border: none;
            border-radius: 6px;
            cursor: pointer;
        }

        .save-btn:hover {
            background-color: #f3f4f6;
        }

        .save-btn.saved {
            color: #2563eb;
        }

        .save-btn-text {
            display: none;
        }

        /* Job Offers Styles */
        .add-to-cart {
            position: absolute;
            top: 1rem;
            right: 1rem;
            display: flex;
            align-items: center;
            gap: 0.375rem;
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
            background-color: #f3f4f6;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .add-to-cart:hover {
            background-color: #e5e7eb;
        }

        .add-to-cart.added {
            background-color: #dcfce7;
            border-color: #86efac;
            color: #166534;
        }

        /* My Clients Styles */
        .clients-section {
            padding: 1.5rem 0;
        }

        .clients-header {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .clients-title {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .clients-search {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .search-input {
            padding: 0.625rem 1rem;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 0.875rem;
            width: 100%;
        }

        .clients-scroll {
            overflow-x: auto;
            padding-bottom: 1rem;
            -webkit-overflow-scrolling: touch;
            scrollbar-width: thin;
            scrollbar-color: #d1d5db transparent;
        }

        .clients-scroll::-webkit-scrollbar {
            height: 8px;
        }

        .clients-scroll::-webkit-scrollbar-track {
            background: transparent;
        }

        .clients-scroll::-webkit-scrollbar-thumb {
            background-color: #d1d5db;
            border-radius: 20px;
        }

        .clients-container {
            display: flex;
            gap: 1.5rem;
            padding: 0.5rem 0.25rem;
        }

        .client-card {
            background-color: white;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
            overflow: hidden;
            position: relative;
            min-width: 300px;
            max-width: 400px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .client-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
        }

        .client-header {
            padding: 1.25rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .client-title {
            font-weight: 600;
            font-size: 1.125rem;
        }

        .favorite-btn {
            position: absolute;
            top: 1rem;
            right: 1rem;
            display: flex;
            align-items: center;
            gap: 0.375rem;
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
            background-color: #f3f4f6;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .favorite-btn:hover {
            background-color: #e5e7eb;
        }

        .favorite-btn.favorited {
            background-color: #dcfce7;
            border-color: #86efac;
            color: #166534;
        }

        .client-body {
            padding: 1.25rem;
        }

        .client-description {
            color: #6b7280;
            margin-bottom: 1.25rem;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
            font-size: 0.9375rem;
            max-height: 4.5em; /* Fallback for non-webkit browsers */
        }

        .client-details {
            display: grid;
            grid-template-columns: 1fr;
            gap: 0.75rem;
        }

        .client-detail {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
        }

        .client-detail i {
            color: #6b7280;
            width: 16px;
            text-align: center;
        }

        .client-footer {
            padding: 1.25rem;
            border-top: 1px solid #e5e7eb;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            animation: fadeIn 0.3s;
        }

        .modal-content {
            background-color: #fff;
            margin: 5% auto;
            padding: 20px;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            position: relative;
            animation: slideIn 0.3s;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #333;
        }

        .modal-description {
            color: #6b7280;
            margin-bottom: 20px;
        }

        .close-modal {
            font-size: 28px;
            font-weight: bold;
            color: #666;
            cursor: pointer;
        }

        .close-modal:hover {
            color: #000;
        }

        /* My Applications Modal Styles */
        .applications-modal {
            max-width: 900px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .applications-header {
            background: linear-gradient(135deg, #004AAD 0%, #CD208B 100%);
            color: white;
            padding: 1.5rem 2rem;
            margin: -20px -20px 20px -20px;
            border-radius: 8px 8px 0 0;
        }

        .applications-header .modal-title {
            color: white;
            font-size: 1.8rem;
            font-weight: 700;
        }

        .applications-header .close {
            color: white;
            font-size: 2rem;
            opacity: 0.8;
        }

        .applications-header .close:hover {
            opacity: 1;
        }

        .applications-subtitle {
            color: #6b7280;
            margin-bottom: 1.5rem;
            font-size: 1rem;
        }

        .applications-container {
            min-height: 300px;
        }

        .loading-applications {
            text-align: center;
            padding: 3rem 1rem;
            color: #6b7280;
        }

        .loading-applications i {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: var(--primary-blue);
        }

        .application-item {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .application-item:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .application-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .application-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .application-company {
            color: #6b7280;
            font-size: 0.9rem;
        }

        .application-status {
            padding: 0.4rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-pending {
            background: #fef3c7;
            color: #d97706;
        }

        .status-accepted,
        .status-accept {
            background: #d1fae5;
            color: #059669;
            box-shadow: 0 2px 6px rgba(16, 185, 129, 0.15);
            font-weight: 600;
        }

        .status-rejected {
            background: #fee2e2;
            color: #dc2626;
        }

        .status-in-review {
            background: #dbeafe;
            color: #2563eb;
        }

        .application-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .application-detail {
            display: flex;
            flex-direction: column;
        }

        .detail-label {
            font-size: 0.8rem;
            color: #6b7280;
            margin-bottom: 0.2rem;
            text-transform: uppercase;
            font-weight: 500;
        }

        .detail-value {
            font-weight: 600;
            color: #333;
        }

        .application-description {
            color: #6b7280;
            font-size: 0.9rem;
            line-height: 1.5;
            margin-bottom: 1rem;
        }

        .application-actions {
            display: flex;
            gap: 0.5rem;
            justify-content: flex-end;
        }

        .action-btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            font-size: 0.85rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-view {
            background: var(--primary-blue);
            color: white;
        }

        .btn-view:hover {
            background: #003a8c;
        }

        .btn-withdraw {
            background: #fee2e2;
            color: #dc2626;
        }

        .btn-withdraw:hover {
            background: #fecaca;
        }

        .no-applications {
            text-align: center;
            padding: 3rem 1rem;
            color: #6b7280;
        }

        .no-applications i {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #d1d5db;
        }

        .no-applications h3 {
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
            color: #374151;
        }

        .no-applications p {
            font-size: 0.9rem;
        }

        /* Job Details Modal Styles */
        .job-details-modal {
            max-width: 900px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .job-details-header {
            background: linear-gradient(135deg, #004AAD 0%, #CD208B 100%);
            color: white;
            padding: 1.5rem 2rem;
            margin: -20px -20px 20px -20px;
            border-radius: 8px 8px 0 0;
        }

        .job-details-header .modal-title {
            color: white;
            font-size: 1.8rem;
            font-weight: 700;
        }

        .job-details-header .close {
            color: white;
            font-size: 2rem;
            opacity: 0.8;
        }

        .job-details-header .close:hover {
            opacity: 1;
        }

        .job-details-container {
            min-height: 300px;
        }

        .loading-job-details {
            text-align: center;
            padding: 3rem 1rem;
            color: #6b7280;
        }

        .loading-job-details i {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: var(--primary-blue);
        }

        .job-detail-section {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .job-detail-section h3 {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #e2e8f0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .job-detail-section h3 i {
            color: var(--primary-blue);
            font-size: 1rem;
        }

        .job-detail-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .job-detail-item {
            display: flex;
            flex-direction: column;
            gap: 0.3rem;
        }

        .job-detail-label {
            font-size: 0.85rem;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .job-detail-value {
            font-size: 1rem;
            font-weight: 500;
            color: #1f2937;
        }

        .job-description-full {
            color: #4b5563;
            font-size: 1rem;
            line-height: 1.6;
            margin-bottom: 1rem;
        }

        .skills-tags-container {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }

        .skill-tag-modal {
            background: linear-gradient(135deg, #dbeafe, #bfdbfe);
            color: #1e40af;
            padding: 0.4rem 0.8rem;
            border-radius: 16px;
            font-size: 0.85rem;
            font-weight: 600;
            border: 1px solid #93c5fd;
        }

        .client-info-modal {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        .client-avatar-modal {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: 2px solid var(--primary-blue);
            overflow: hidden;
            position: relative;
        }

        .client-avatar-modal img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
        }

        .client-details-modal {
            flex: 1;
        }

        .client-name-modal {
            font-size: 1.1rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 0.2rem;
        }

        .client-rating-modal {
            display: flex;
            align-items: center;
            gap: 0.3rem;
            color: #f59e0b;
            font-size: 0.9rem;
        }

        .client-location {
            margin-left: 0.5rem;
            color: #6b7280;
            font-size: 0.85rem;
        }

        .job-details-content {
            padding: 0;
        }

        .job-info-section,
        .job-description-section,
        .project-description-section,
        .requirements-section,
        .skills-section {
            margin-bottom: 2rem;
        }

        .job-info-section h4,
        .job-description-section h4,
        .project-description-section h4,
        .requirements-section h4,
        .skills-section h4 {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--primary-blue);
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #f3f4f6;
        }

        .job-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        .info-item {
            display: flex;
            flex-direction: column;
            gap: 0.3rem;
        }

        .info-item label {
            font-weight: 600;
            color: #374151;
            font-size: 0.9rem;
        }

        .info-item span {
            color: #6b7280;
            font-size: 0.9rem;
        }

        .job-description-content,
        .project-description-content,
        .requirements-content {
            line-height: 1.6;
            color: #374151;
            background: #f9fafb;
            padding: 1rem;
            border-radius: 8px;
            border-left: 4px solid var(--primary-blue);
        }

        .skills-container {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .no-skills {
            color: #9ca3af;
            font-style: italic;
        }

        .modal-actions {
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 2px solid #f3f4f6;
            text-align: center;
        }

        .apply-btn {
            background: linear-gradient(90deg, var(--primary-blue), var(--primary-pink));
            color: white;
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .apply-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 74, 173, 0.3);
        }

        .error-job-details,
        .loading-job-details {
            text-align: center;
            padding: 3rem 2rem;
            color: #6b7280;
        }

        .error-job-details i,
        .loading-job-details i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .error-job-details i {
            color: #ef4444;
        }

        .loading-job-details i {
            color: var(--primary-blue);
        }

        /* Application Confirmation Modal Styles */
        #applicationConfirmModal {
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .application-confirm-modal {
            max-width: 420px;
            text-align: center;
            border-radius: 12px;
            box-shadow: 0 20px 40px -12px rgba(0, 0, 0, 0.25);
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            transform: scale(0.95);
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .application-confirm-modal .modal-header {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-pink) 100%);
            color: white;
            border-radius: 12px 12px 0 0;
            padding: 1rem 1.5rem;
            position: relative;
            overflow: hidden;
        }

        .application-confirm-modal .modal-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .application-confirm-modal .modal-header h2 {
            margin: 0;
            font-size: 1.2rem;
            font-weight: 700;
            position: relative;
            z-index: 1;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .application-confirm-modal .close {
            color: white;
            font-size: 1.8rem;
            font-weight: bold;
            position: relative;
            z-index: 1;
            opacity: 0.8;
            transition: all 0.3s ease;
        }

        .application-confirm-modal .close:hover {
            opacity: 1;
            transform: scale(1.1);
        }

        .confirmation-content {
            padding: 1.5rem 1.5rem;
        }

        .confirmation-icon {
            margin-bottom: 1rem;
            position: relative;
        }

        .confirmation-icon::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, rgba(0, 74, 173, 0.1) 0%, rgba(236, 72, 153, 0.1) 100%);
            border-radius: 50%;
            z-index: 0;
        }

        .confirmation-icon i {
            font-size: 3rem;
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-pink) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            z-index: 1;
            filter: drop-shadow(0 4px 8px rgba(0, 74, 173, 0.2));
        }

        .confirmation-content h3 {
            font-size: 1.2rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 1rem;
            line-height: 1.4;
            font-family: 'Poppins', sans-serif;
        }

        .confirm-job-title {
            font-size: 1rem;
            font-weight: 600;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            color: var(--primary-blue);
            margin-bottom: 1.25rem;
            padding: 0.75rem 1rem;
            border-radius: 8px;
            border: 2px solid rgba(0, 74, 173, 0.1);
            box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .confirm-job-title::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-pink) 100%);
        }

        .confirmation-details {
            margin-bottom: 1.5rem;
            padding: 0.875rem 1rem;
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border-radius: 8px;
            border: 2px solid rgba(245, 158, 11, 0.2);
            box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        .confirmation-details::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            border-radius: 2px 0 0 2px;
        }

        .confirmation-details p {
            margin: 0;
            color: #92400e;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            justify-content: center;
            line-height: 1.4;
        }

        .confirmation-details i {
            color: #f59e0b;
            font-size: 1.1rem;
        }

        .confirmation-actions {
            display: flex;
            gap: 1.25rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .cancel-btn,
        .confirm-apply-btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            min-width: 130px;
            justify-content: center;
            font-family: 'Poppins', sans-serif;
            position: relative;
            overflow: hidden;
        }

        .cancel-btn {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: #64748b;
            border: 2px solid #e2e8f0;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .cancel-btn:hover {
            background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
            color: #475569;
            transform: translateY(-2px);
            box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.1);
        }

        .confirm-apply-btn {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-pink) 100%);
            color: white;
            border: 2px solid transparent;
            box-shadow: 0 8px 15px -3px rgba(0, 74, 173, 0.4);
            position: relative;
        }

        .confirm-apply-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .confirm-apply-btn:hover::before {
            left: 100%;
        }

        .confirm-apply-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 25px -5px rgba(0, 74, 173, 0.4);
        }

        .confirm-apply-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .confirm-apply-btn:disabled:hover {
            transform: none;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        /* Withdraw Confirmation Modal Styles */
        #withdrawConfirmModal {
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .withdraw-confirm-modal {
            max-width: 420px;
            text-align: center;
            border-radius: 12px;
            box-shadow: 0 20px 40px -12px rgba(0, 0, 0, 0.25);
            background: linear-gradient(135deg, #ffffff 0%, #fef2f2 100%);
            border: 1px solid rgba(239, 68, 68, 0.1);
            backdrop-filter: blur(10px);
            transform: scale(0.95);
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .withdraw-confirm-modal .modal-header {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            border-radius: 12px 12px 0 0;
            padding: 1rem 1.5rem;
            position: relative;
            overflow: hidden;
        }

        .withdraw-confirm-modal .modal-header h2 {
            margin: 0;
            font-size: 1.2rem;
            font-weight: 700;
            position: relative;
            z-index: 1;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .withdraw-icon::before {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.1) 100%);
        }

        .withdraw-icon i {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            filter: drop-shadow(0 4px 8px rgba(239, 68, 68, 0.2));
        }

        .withdraw-job-title {
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
            color: #dc2626;
            border: 2px solid rgba(239, 68, 68, 0.1);
        }

        .withdraw-job-title::before {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        }

        .withdraw-warning {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 2px solid rgba(245, 158, 11, 0.2);
        }

        .withdraw-warning::before {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }

        .confirm-withdraw-btn {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            border: 2px solid transparent;
            box-shadow: 0 8px 15px -3px rgba(239, 68, 68, 0.4);
        }

        .confirm-withdraw-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 25px -5px rgba(239, 68, 68, 0.4);
        }

        .confirm-withdraw-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        /* Already Applied Message Styles */
        .already-applied-message {
            text-align: center;
            padding: 1.5rem;
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
            border-radius: 12px;
            border: 2px solid rgba(34, 197, 94, 0.2);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .applied-status-container {
            display: flex;
            align-items: center;
            gap: 1rem;
            justify-content: center;
        }

        .applied-icon {
            flex-shrink: 0;
        }

        .applied-icon i {
            font-size: 3rem;
            color: #22c55e;
            filter: drop-shadow(0 4px 8px rgba(34, 197, 94, 0.3));
        }

        .applied-text {
            text-align: left;
        }

        .applied-text h4 {
            margin: 0 0 0.5rem 0;
            font-size: 1.2rem;
            font-weight: 700;
            color: #166534;
            font-family: 'Poppins', sans-serif;
        }

        .applied-text p {
            margin: 0 0 0.75rem 0;
            color: #15803d;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-pending {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            color: #92400e;
            border: 1px solid rgba(245, 158, 11, 0.3);
        }

        .status-accepted,
        .status-accept {
            background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
            color: #166534;
            border: 1px solid rgba(34, 197, 94, 0.3);
            box-shadow: 0 2px 8px rgba(34, 197, 94, 0.2);
            font-weight: 700;
        }

        .status-rejected {
            background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
            color: #991b1b;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        /* Accepted application styling */
        .application-accepted {
            border: 2px solid #10b981 !important;
            background: linear-gradient(135deg, #f0fdf4 0%, #ffffff 100%);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.15);
            position: relative;
        }

        .application-accepted::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #10b981, #059669);
            border-radius: 8px 8px 0 0;
        }

        .acceptance-notice {
            background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
            border: 1px solid #a7f3d0;
            border-radius: 8px;
            padding: 12px 16px;
            margin-top: 12px;
            color: #065f46;
            font-size: 14px;
        }

        .acceptance-notice i {
            color: #10b981;
            margin-right: 8px;
        }

        .btn-tracker {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            border: none;
            transition: all 0.3s ease;
        }

        .btn-tracker:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .text-success {
            color: #10b981 !important;
        }

        /* Responsive adjustments for applied message */
        @media (max-width: 640px) {
            .applied-status-container {
                flex-direction: column;
                gap: 1rem;
            }

            .applied-text {
                text-align: center;
            }

            .applied-icon i {
                font-size: 2.5rem;
            }
        }

        /* Responsive adjustments */
        @media (max-width: 640px) {
            .application-confirm-modal,
            .withdraw-confirm-modal {
                max-width: 90vw;
                margin: 1rem;
            }

            .confirmation-content {
                padding: 1.25rem 1rem;
            }

            .confirmation-actions {
                flex-direction: column;
                align-items: center;
                gap: 0.75rem;
            }

            .cancel-btn,
            .confirm-apply-btn,
            .confirm-withdraw-btn {
                width: 100%;
                max-width: 240px;
            }
        }

        .apply-job-btn {
            background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 0.8rem 2rem;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 1rem;
        }

        .apply-job-btn:hover {
            background: linear-gradient(90deg, #20c997 0%, #28a745 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }

        .apply-job-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        .radio-option {
            display: block;
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .radio-option:hover {
            background-color: #f9fafb;
        }

        .radio-input {
            margin-right: 10px;
        }

        .radio-label {
            font-weight: 600;
            margin-left: 0.5rem;
        }

        .info {
            margin-left: 1.5rem;
            color: #6b7280;
            margin-top: 0.5rem;
        }

        .buttons {
            margin-top: 2rem;
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
        }

        .buttons button {
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
        }

        .buttons .cancel {
            background: #f3f4f6;
            border: 1px solid #d1d5db;
        }

        .buttons .save {
            background: #d41b8c;
            color: white;
            border: none;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from { transform: translateY(-20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        /* Responsive Styles */
        @media (min-width: 640px) {
            .header-content {
                padding: 0 1.5rem;
            }

            .main-content {
                padding: 1.5rem 1.5rem 3rem;
            }

            .profile-info {
                flex-direction: row;
                align-items: center;
            }

            .profile-meta {
                flex-direction: row;
                gap: 1rem;
            }

            .jobs-filters {
                flex-direction: row;
                gap: 0.75rem;
            }

            .filter-select, .filter-input {
                width: auto;
            }

            .job-details {
                grid-template-columns: repeat(2, 1fr);
            }

            .save-btn-text {
                display: inline;
            }

            .clients-search {
                flex-direction: row;
                gap: 0.75rem;
            }

            .search-input {
                width: auto;
            }

            .client-details {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (min-width: 768px) {
            .header-content {
                padding: 0 1.5rem;
            }

            .nav-links {
                display: flex;
            }

            .mobile-menu-btn {
                display: none;
            }

            .profile-content {
                flex-direction: row;
                justify-content: space-between;
                align-items: center;
            }

            .dashboard-content {
                flex-direction: row;
            }

            .filters {
                width: 280px;
                align-self: flex-start;
                position: sticky;
                top: 20px;
            }

            .jobs-container {
                flex: 1;
                margin-left: 1.5rem;
            }

            /* Stats grid is now flex-based */

            .jobs-header {
                flex-direction: row;
                justify-content: space-between;
                align-items: center;
            }

            .jobs-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .clients-header {
                flex-direction: row;
                justify-content: space-between;
                align-items: center;
            }
        }

        @media (min-width: 1024px) {
            .header-content {
                padding: 0 2rem;
            }

            .main-content {
                padding: 2rem 2rem 4rem;
            }

            .filters {
                width: 320px;
            }

            .client-card {
                min-width: 350px;
            }
        }

        /* Animation for filter sections */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Enhanced search container styling */
        .enhanced-search-container {
            margin-bottom: 1.5rem;
            width: 100%;
        }

        .search-input-wrapper {
            display: flex;
            position: relative;
            width: 100%;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            border-radius: 12px;
            overflow: hidden;
            border: 1px solid #e5e7eb;
            transition: all 0.3s ease;
        }

        .search-input-wrapper:focus-within {
            border-color: #004AAD;
            box-shadow: 0 6px 20px rgba(0, 74, 173, 0.15);
            transform: translateY(-2px);
        }

        .enhanced-search-input {
            flex: 1;
            padding: 1rem 1.5rem;
            border: none;
            font-size: 1rem;
            background-color: #fff;
            transition: all 0.3s ease;
        }

        .enhanced-search-input:focus {
            outline: none;
        }

        .search-button {
            background: linear-gradient(to right, #004AAD, #0063e5);
            border: none;
            padding: 0 1.5rem;
            color: white;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .search-button:hover {
            background: linear-gradient(to right, #003b8a, #0055c8);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 74, 173, 0.3);
        }

        .search-button:active, .search-button.active {
            background: linear-gradient(to right, #003070, #004aad);
            transform: translateY(0);
            box-shadow: 0 2px 6px rgba(0, 74, 173, 0.2);
        }

        .search-button i {
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .search-button:active i, .search-button.active i {
            transform: scale(0.9);
        }
    </style>
    <style>
    .profile-dropdown-content a:first-child:hover,
    .profile-dropdown-content a:first-child:focus,
    .profile-dropdown-content a:first-child:active {
        background: none !important;
        background-color: transparent !important;
        color: var(--primary-blue) !important;
        outline: none !important;
        box-shadow: none !important;
    }
    .filters {
        transition: box-shadow 0.35s, transform 0.35s;
    }
    .filters:hover {
        box-shadow: 0 16px 32px rgba(0, 0, 0, 0.12);
        transform: translateY(-2px);
    }
    .filters::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 0;
        background: linear-gradient(90deg, #004AAD, #CD208B);
        opacity: 0;
        transition: height 0.3s, opacity 0.3s;
        z-index: 1;
    }
    .filters:hover::before {
        height: 8px;
        opacity: 1;
    }
    .profile-btn {
        transition: box-shadow 0.25s, transform 0.25s, background 0.25s, color 0.25s;
    }
    .profile-btn:hover, .profile-btn:focus {
        box-shadow: 0 6px 18px rgba(205, 32, 139, 0.13);
        transform: translateY(-2px) scale(1.04);
        background: linear-gradient(90deg, #f8e1f0 0%, #e9f0ff 100%);
        color: #CD208B;
    }
    .profile-btn:hover i, .profile-btn:focus i {
        color: #CD208B;
    }

    /* Enhanced Job Card Styles */
    .modern-job-card {
        background: #fff;
        border-radius: 18px;
        box-shadow: 0 4px 24px rgba(0, 74, 173, 0.10), 0 1.5px 4px rgba(205, 32, 139, 0.04);
        border: 1px solid #e5e7eb;
        margin-bottom: 2rem;
        transition: box-shadow 0.25s, transform 0.25s;
        padding: 0;
        overflow: hidden;
    }
    .modern-job-card:hover {
        box-shadow: 0 8px 32px rgba(0, 74, 173, 0.18), 0 2px 8px rgba(205, 32, 139, 0.08);
        transform: translateY(-4px) scale(1.012);
        border-color: #CD208B;
    }
    .modern-job-card .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1.2rem 1.5rem 0.7rem 1.5rem;
        border-bottom: 1px solid #f0f0f0;
        background: linear-gradient(90deg, #f8f9ff 80%, #fce4f6 100%);
    }
    .modern-job-card .job-title-main {
        font-size: 1.18rem;
        font-weight: 700;
        color: #004AAD;
        margin-bottom: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 70vw;
    }
    .modern-job-card .posted-date {
        font-size: 0.95rem;
        color: #888;
        font-weight: 500;
    }
    .modern-job-card .card-body {
        padding: 1.3rem 1.7rem 1.1rem 1.7rem;
        display: flex;
        flex-direction: column;
        gap: 1.1rem;
    }
    .modern-job-card .client-profile-section {
        display: flex;
        align-items: center;
        gap: 1.5rem;
        margin-bottom: 1.1rem;
        min-width: 0;
        padding: 0.7rem 1rem;
        background: #f8f9fb;
        border-radius: 10px;
        border: 1px solid #f0f0f0;
    }
    .modern-job-card .client-details {
        flex: 1 1 auto;
        min-width: 0;
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: 0.12rem;
        align-items: flex-start;
    }
    .modern-job-card .client-name-section {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.05rem;
    }
        .modern-job-card .client-avatar-container {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #fff;
        border: 2px solid #CD208B;
        box-shadow: 0 2px 8px rgba(205, 32, 139, 0.08);
    }
    .modern-job-card .client-avatar-container img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        object-fit: cover;
        display: block;
    }
    .modern-job-card .client-details {
        flex: 1;
    }
    .modern-job-card .client-business-name {
        font-size: 1.08rem;
        font-weight: 700;
        color: #222;
        margin-bottom: 0.1rem;
    }
    .modern-job-card .client-rating {
        display: flex;
        align-items: center;
        gap: 0.3rem;
        font-size: 0.93rem;
        color: #FFD700;
        font-weight: 500;
        margin-bottom: 0.1rem;
    }
    .modern-job-card .client-specialty {
        font-size: 0.91rem;
        color: #888;
        font-style: italic;
    }
    .modern-job-card .client-location-section {
        font-size: 0.91rem;
        color: #888;
        display: flex;
        align-items: center;
        gap: 0.3rem;
        margin-top: 0.1rem;
    }
            .modern-job-card .job-description-section {
        margin: 1.1rem 0 1.3rem 0;
        color: #222;
        font-size: 1.08rem;
        font-weight: 500;
        min-height: 38px;
        line-height: 1.6;
        max-height: 4.2em;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: normal;
        word-break: break-word;
        background: #f7f9fc;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
        box-shadow: 0 2px 8px rgba(0,74,173,0.04);
        padding: 1rem 1.2rem;
        margin-bottom: 1.3rem;
    }
    .modern-job-card .job-details-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
        gap: 0.6rem 1.1rem;
        margin-bottom: 0.5rem;
        width: 100%;
        min-width: 0;
        border-top: 1px solid #f0f0f0;
        padding-top: 0.7rem;
    }
    .modern-job-card .detail-item {
        display: flex;
        align-items: center;
        gap: 0.4rem;
        font-size: 0.96rem;
        color: #555;
        background: #fff;
        border-radius: 6px;
        padding: 0.28rem 0.6rem;
        min-width: 0;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .modern-job-card .budget-type-item {
        background: #fce4f6;
        color: #CD208B;
        font-weight: 600;
    }
    .modern-job-card .card-footer {
        padding: 0.9rem 1.7rem;
        border-top: 1px solid #f0f0f0;
        background: #fff;
        display: flex;
        justify-content: flex-end;
    }
    .modern-job-card .view-details-btn {
        background: linear-gradient(90deg, #004AAD 60%, #CD208B 100%);
        color: #fff;
        border: none;
        border-radius: 14px;
        padding: 0.38rem 1rem;
        font-size: 0.97rem;
        font-weight: 600;
        cursor: pointer;
        transition: background 0.22s, box-shadow 0.22s, transform 0.18s;
        box-shadow: 0 2px 8px rgba(0, 74, 173, 0.10);
        letter-spacing: 0.01em;
        margin-left: 0.7rem;
        display: inline-block;
    }
    .modern-job-card .view-details-btn:hover {
        background: linear-gradient(90deg, #CD208B 0%, #004AAD 100%);
        box-shadow: 0 6px 20px rgba(205, 32, 139, 0.18);
        transform: translateY(-2px) scale(1.04);
    }
    .modern-job-card .card-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 0.5rem;
    }
    .modern-job-card .footer-info {
        flex: 1;
    }
    .modern-job-card .footer-info small {
        font-size: 0.75rem;
        color: #6b7280;
        font-style: italic;
        display: flex;
        align-items: center;
        gap: 0.3rem;
    }
    .modern-job-card .footer-info i {
        color: #004AAD;
        font-size: 0.8rem;
    }
    .modern-job-card .skills-item {
        max-width: 100%;
        overflow: hidden;
    }
    .modern-job-card .skills-item span {
        display: block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .modern-job-card .skills-more {
        color: #6b7280;
        font-style: italic;
        font-weight: normal;
        margin-left: 0.3rem;
        background: #f3f4f6;
        padding: 0.1rem 0.4rem;
        border-radius: 10px;
        font-size: 0.8rem;
    }

    /* Unified Notification System Styles - Exact copy from client_page.html */
    .unified-notification {
        position: fixed;
        padding: 18px 24px;
        border-radius: 16px;
        color: white;
        font-weight: 500;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        z-index: 10000;
        opacity: 0;
        transform: translateY(100px);
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        max-width: 400px;
        min-width: 300px;
        display: flex;
        align-items: center;
        font-family: 'Poppins', system-ui, -apple-system, sans-serif;
        border-left: 4px solid;
        background: linear-gradient(135deg, rgba(0, 74, 173, 0.95), rgba(205, 32, 139, 0.95));
        cursor: pointer;
    }

    .unified-notification.show {
        opacity: 1;
        transform: translateY(0);
    }

    .unified-notification.success {
        background: linear-gradient(135deg, rgba(16, 185, 129, 0.95), rgba(0, 148, 133, 0.95));
        border-left-color: #10b981;
    }

    .unified-notification.error {
        background: linear-gradient(135deg, rgba(239, 68, 68, 0.95), rgba(220, 38, 38, 0.95));
        border-left-color: #ef4444;
    }

    .unified-notification.warning {
        background: linear-gradient(135deg, rgba(245, 158, 11, 0.95), rgba(217, 119, 6, 0.95));
        border-left-color: #f59e0b;
    }

    .unified-notification.info {
        background: linear-gradient(135deg, rgba(0, 74, 173, 0.95), rgba(59, 130, 246, 0.95));
        border-left-color: var(--primary-blue);
    }

    .unified-notification.application {
        background: linear-gradient(135deg, rgba(139, 69, 19, 0.95), rgba(160, 82, 45, 0.95));
        border-left-color: #8b4513;
    }

    .unified-notification.contract {
        background: linear-gradient(135deg, rgba(75, 0, 130, 0.95), rgba(138, 43, 226, 0.95));
        border-left-color: #4b0082;
    }

    .unified-notification .notification-icon {
        margin-right: 15px;
        font-size: 1.3rem;
        flex-shrink: 0;
    }

    .unified-notification .notification-content {
        flex: 1;
    }

    .unified-notification .notification-title {
        font-weight: 600;
        margin-bottom: 4px;
        font-size: 1rem;
    }

    .unified-notification .notification-message {
        font-size: 0.9rem;
        opacity: 0.9;
        line-height: 1.4;
    }

    .unified-notification .notification-close {
        background: none;
        border: none;
        color: white;
        opacity: 0.7;
        cursor: pointer;
        font-size: 1.1rem;
        padding: 0;
        margin-left: 15px;
        transition: all 0.2s ease;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
    }

    .unified-notification .notification-close:hover {
        opacity: 1;
        background: rgba(255, 255, 255, 0.2);
        transform: scale(1.1);
    }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar">
        <div class="navbar-left">
                <a href="{{ url_for('landing_page') }}" style="text-decoration: none;">
                    <div class="logo">
                        <img src="{{ url_for('static', filename='img/giggenius_logo.jpg') }}" alt="GigGenius Logo">
                        <h1>GigGenius</h1>
                    </div>
                </a>
                <button class="mobile-menu-btn" id="mobileMenuBtn">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="nav-links" id="navLinks">
                    <a href="{{ url_for('genius_page') }}">Find Gigs</a>
                    <a href="{{ url_for('my_proposal') }}" id="navLinks">Proposal</a>

                    <!-- Contracts Dropdown -->
                    <div class="nav-dropdown">
                        <button class="nav-dropbtn">Contracts
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="nav-dropdown-content">
                            <a href="{{ url_for('tracker') }}">Log Hours</a>
                        </div>
                    </div>

                    <!-- Earnings Dropdown -->
                    <div class="nav-dropdown">
                        <button class="nav-dropbtn">Earnings
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="nav-dropdown-content">
                            <a href="{{ url_for('billing_and_earnings') }}">Billings and Earnings</a>
                            <a href="{{ url_for('withdraw_earnings') }}">Withdraw Earnings</a>
                            <a href="{{ url_for('tax_info') }}">Tax Info</a>
                        </div>
                    </div>

                    <a href="{{ url_for('messages') }}">Messages</a>
                </div>
            </div>
            <div class="right-section">
                <div class="search-container">
                    <div class="search-bar">
                        <input type="text" id="searchInput" placeholder="Search..." onkeypress="if(event.key==='Enter') performSearch()">
                        <i class="fas fa-search icon" onclick="performSearch()"></i>
                    </div>
                </div>
                <div class="auth-buttons">
                    <div class="notification-container">
                        <div class="notification-icon" id="notification-bell">
                            <i class="fas fa-bell"></i>
                            <span id="notification-count" class="notification-badge" style="display: none;">0</span>
                        </div>
                        <div class="notification-dropdown">
                            <div class="notification-header">
                                <span>Notifications</span>
                                <span class="notification-header-actions" id="mark-all-read">Mark all as read</span>
                            </div>
                            <div id="notification-list">
                                <!-- Notifications will be loaded here -->
                            </div>
                            <div id="empty-notifications" class="empty-notifications" style="display: none;">
                                <i class="far fa-bell-slash"></i>
                                <h3>No notifications yet</h3>
                                <p>You'll see application updates and important notifications here</p>
                            </div>
                        </div>
                    </div>
                    <div class="profile-dropdown">
                        <div class="profile-button">
                            <img src="{{ url_for('api_profile_photo', user_type='genius', user_id=genius.id) }}" alt="Profile Picture">
                        </div>
                        <div class="profile-dropdown-content">
                            <a href="{{ url_for('genius_profile') }}">
                                <i class="fas fa-user"></i> My Profile
                            </a>
                            <a href="{{ url_for('landing_page') }}">
                                <i class="fas fa-cog"></i> Account Settings
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="{{ url_for('logout') }}" class="logout-option" style="color: #dc3545 !important; font-weight: bold;">
                                <i class="fas fa-sign-out-alt"></i> Log Out
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </nav>


    <!-- Mobile Menu -->
    <div class="mobile-menu" id="mobileMenu">
        <div class="mobile-menu-content">
            <div class="mobile-menu-header">
                <div class="logo">
                    <img src="{{ url_for('static', filename='img/giggenius_logo.jpg') }}" alt="GigGenius Logo">
                    <h1>GigGenius</h1>
                </div>
                <button class="mobile-close-btn" id="mobileCloseBtn">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- Mobile Profile Section -->
            <div class="mobile-profile-section">
                <div class="profile-button" style="margin-bottom: 1rem;">
                    <img src="{{ url_for('api_profile_photo', user_type='genius', user_id=genius.id) }}" alt="Profile Picture">
                </div>
            </div>

            <!-- Mobile Navigation Links -->
            <a href="{{ url_for('find_gigs') }}">
                <i class="fas fa-briefcase"></i> Find Gigs
            </a>
            <a href="#" id="mobileMyApplicationsLink">
                <i class="fas fa-folder"></i> My Applications
            </a>
            <a href="{{ url_for('landing_page') }}">
                <i class="fas fa-handshake"></i> Contracts
            </a>
            <a href="{{ url_for('tracker') }}">
                <i class="fas fa-clock"></i> Log Hours
            </a>
            <a href="{{ url_for('billing_and_earnings') }}">
                <i class="fas fa-chart-line"></i> Earnings
            </a>
            <a href="{{ url_for('messages') }}">
                <i class="fas fa-envelope"></i> Messages
            </a>

            <!-- Mobile Profile Actions -->
            <div class="mobile-profile-actions">
                <a href="{{ url_for('genius_profile') }}" class="mobile-profile-link">
                    <i class="fas fa-user"></i> My Profile
                </a>
                <a href="{{ url_for('landing_page') }}" class="mobile-profile-link">
                    <i class="fas fa-cog"></i> Account Settings
                </a>
                <a href="{{ url_for('logout') }}" class="mobile-profile-link logout">
                    <i class="fas fa-sign-out-alt"></i> Log Out
                </a>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Profile Section -->
        <section class="profile-section">
            <div class="profile-card">
                <div class="profile-content">
                    <div class="profile-info">
                        <a href="{{ url_for('genius_profile') }}" class="profile-avatar" style="cursor: pointer; text-decoration: none;">
                            <img src="{{ url_for('api_profile_photo', user_type='genius', user_id=genius.id) }}" alt="{{ genius.first_name }} {{ genius.last_name }}">
                        </a>
                        <div class="profile-details">
                            <h2 class="profile-name">
                                Welcome <a href="{{ url_for('genius_profile') }}" style="text-decoration: none; color: inherit; cursor: pointer;"><span>{{ genius.first_name }} {{ genius.last_name }}</span></a>
                            </h2>
                            <div class="profile-meta">
                                <div class="profile-meta-item">
                                    <i class="fas fa-briefcase"></i>
                                    <span>{{ genius.position }}</span>
                                </div>
                                <div class="profile-meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>{{ genius.country }}</span>
                                </div>
                            </div>
                            <div class="profile-actions">
                                <button class="profile-btn btn-outline active" id="dashboardBtn">
                                    <i class="fas fa-home"></i>
                                    <span>Dashboard</span>
                                </button>
                                <a href="{{ url_for('genius_profile') }}" class="profile-btn btn-outline" style="text-decoration: none;">
                                <i class="fas fa-user"></i>
                                <span>Profile</span>
                                </a>
                                <button class="profile-btn btn-outline" id="myApplicationsBtn">
                                    <i class="fas fa-folder"></i>
                                    <span>My Applications</span>
                                </button>
                                <button class="profile-btn btn-outline" id="jobOffersBtn">
                                    <i class="fas fa-bell"></i>
                                    <span>Job Offers</span>
                                </button>
                                <button class="profile-btn btn-outline" id="myClientsBtn">
                                    <i class="fas fa-star"></i>
                                    <span>My Clients</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Stats Section -->
        <section class="stats-section">
            <div class="stats-grid">
                <div class="stat-card">
                    <i class="fas fa-dollar-sign stat-icon"></i>
                    <div class="stat-value">${{ genius.hourly_rate|default('0') }}</div>
                    <div class="stat-label">Hourly Rate</div>
                </div>
                <div class="stat-card">
                    <i class="fas fa-coins stat-icon"></i>
                    <div class="stat-value">$0</div>
                    <div class="stat-label">Total Earnings</div>
                </div>
                <div class="stat-card">
                <i class="fas fa-briefcase stat-icon"></i>
                <div class="stat-value">0</div>
                <div class="stat-label">Hired</div>
                </div>
                <div class="stat-card">
                <i class="fas fa-check-circle stat-icon"></i>
                <div class="stat-value">0</div>
                <div class="stat-label">Complete Job</div>
                </div>
                </div>
                </section>

        <!-- Introduction Section -->
        <section class="introduction-section" id="introductionSection">
            <h3>Introduction</h3>
            <div class="text-wrapper">
                {% set intro_limit = 200 %}
                {% if genius.introduction and genius.introduction|length > intro_limit %}
                    <p id="introText">
                        {{ genius.introduction[:intro_limit] }}<span id="dots">...</span><span id="moreText" style="display:none;">{{ genius.introduction[intro_limit:] }}</span>
                    </p>
                    <a href="javascript:void(0);" id="showMoreBtn" class="show-more-btn" style="text-decoration: none; color: #d12b82; cursor: pointer;">See More</a>
                {% else %}
                    <p>{{ genius.introduction }}</p>
                {% endif %}
            </div>
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    var showMoreBtn = document.getElementById('showMoreBtn');
                    if (showMoreBtn) {
                        showMoreBtn.addEventListener('click', function() {
                            var dots = document.getElementById('dots');
                            var moreText = document.getElementById('moreText');
                            if (dots.style.display === 'none') {
                                dots.style.display = 'inline';
                                moreText.style.display = 'none';
                                showMoreBtn.textContent = 'See More';
                            } else {
                                dots.style.display = 'none';
                                moreText.style.display = 'inline';
                                showMoreBtn.textContent = 'See Less';
                            }
                        });
                    }
                });
            </script>
        </section>

        <!-- Quote Slider -->
        <div class="quote-slider" id="quoteSlider">
            <h1 class="quote" id="currentQuote">
                <span class="main-text">Getting a job shouldn't be expensive.</span>
                <span class="sub-text">No connect or bids needed.</span>
            </h1>
            <div class="quote-navigation">
                <span class="quote-arrow left" onclick="changeQuote(-1)">
                    <i class="fas fa-chevron-left"></i>
                </span>
                <span class="quote-arrow right" onclick="changeQuote(1)">
                    <i class="fas fa-chevron-right"></i>
                </span>
            </div>
            <div class="quote-dots">
                <span class="quote-dot active" onclick="jumpToQuote(0)"></span>
                <span class="quote-dot" onclick="jumpToQuote(1)"></span>
                <span class="quote-dot" onclick="jumpToQuote(2)"></span>
            </div>
        </div>

        <!-- Dashboard Content (Default) -->
        <div id="dashboardContent" class="content-section active">
            <div class="dashboard-content">
                <!-- Filters Sidebar -->
                <aside class="filters">
                    <div class="filters-header">
                        <h2>Filter Gigs</h2>
                        <button id="clearFilters" class="clear-filters-btn">
                            <i class="fas fa-times"></i> Clear Filters
                        </button>
                    </div>

                    <div class="filter-group">
                        <label for="locationFilter">Location</label>
                        <div class="custom-select-wrapper">
                            <div class="custom-select" id="customLocationSelect">
                                <div class="custom-select__trigger">
                                    <span id="locationDisplay">All Locations</span>
                                    <div class="arrow"></div>
                                </div>
                                <div class="custom-options">
                                    <span class="custom-option selected" data-value="">All Locations</span>

                                    <span class="option-group-label">Popular</span>
                                    <span class="custom-option" data-value="Remote">Remote</span>
                                    <span class="custom-option" data-value="United States">United States</span>
                                    <span class="custom-option" data-value="United Kingdom">United Kingdom</span>
                                    <span class="custom-option" data-value="Canada">Canada</span>
                                    <span class="custom-option" data-value="Australia">Australia</span>

                                    <span class="option-group-label">Europe</span>
                                    <span class="custom-option" data-value="Germany">Germany</span>
                                    <span class="custom-option" data-value="France">France</span>
                                    <span class="custom-option" data-value="Spain">Spain</span>
                                    <span class="custom-option" data-value="Italy">Italy</span>
                                    <span class="custom-option" data-value="Netherlands">Netherlands</span>
                                    <span class="custom-option" data-value="Sweden">Sweden</span>
                                    <span class="custom-option" data-value="Switzerland">Switzerland</span>
                                    <span class="custom-option" data-value="Poland">Poland</span>
                                    <span class="custom-option" data-value="Belgium">Belgium</span>
                                    <span class="custom-option" data-value="Norway">Norway</span>
                                    <span class="custom-option" data-value="Denmark">Denmark</span>
                                    <span class="custom-option" data-value="Finland">Finland</span>
                                    <span class="custom-option" data-value="Ireland">Ireland</span>
                                    <span class="custom-option" data-value="Portugal">Portugal</span>
                                    <span class="custom-option" data-value="Austria">Austria</span>
                                    <span class="custom-option" data-value="Greece">Greece</span>

                                    <span class="option-group-label">Asia</span>
                                    <span class="custom-option" data-value="India">India</span>
                                    <span class="custom-option" data-value="Japan">Japan</span>
                                    <span class="custom-option" data-value="China">China</span>
                                    <span class="custom-option" data-value="Singapore">Singapore</span>
                                    <span class="custom-option" data-value="Philippines">Philippines</span>
                                    <span class="custom-option" data-value="Malaysia">Malaysia</span>
                                    <span class="custom-option" data-value="Indonesia">Indonesia</span>
                                    <span class="custom-option" data-value="Thailand">Thailand</span>
                                    <span class="custom-option" data-value="Vietnam">Vietnam</span>
                                    <span class="custom-option" data-value="South Korea">South Korea</span>
                                    <span class="custom-option" data-value="Hong Kong">Hong Kong</span>
                                    <span class="custom-option" data-value="Taiwan">Taiwan</span>
                                    <span class="custom-option" data-value="United Arab Emirates">United Arab Emirates</span>
                                    <span class="custom-option" data-value="Israel">Israel</span>

                                    <span class="option-group-label">Americas</span>
                                    <span class="custom-option" data-value="Brazil">Brazil</span>
                                    <span class="custom-option" data-value="Mexico">Mexico</span>
                                    <span class="custom-option" data-value="Argentina">Argentina</span>
                                    <span class="custom-option" data-value="Colombia">Colombia</span>
                                    <span class="custom-option" data-value="Chile">Chile</span>
                                    <span class="custom-option" data-value="Peru">Peru</span>

                                    <span class="option-group-label">Africa & Middle East</span>
                                    <span class="custom-option" data-value="South Africa">South Africa</span>
                                    <span class="custom-option" data-value="Nigeria">Nigeria</span>
                                    <span class="custom-option" data-value="Kenya">Kenya</span>
                                    <span class="custom-option" data-value="Egypt">Egypt</span>
                                    <span class="custom-option" data-value="Morocco">Morocco</span>
                                    <span class="custom-option" data-value="Ghana">Ghana</span>

                                    <span class="option-group-label">Oceania</span>
                                    <span class="custom-option" data-value="New Zealand">New Zealand</span>
                                    <span class="custom-option" data-value="Fiji">Fiji</span>
                                </div>
                            </div>
                            <input type="hidden" id="locationFilter" name="locationFilter" value="">
                        </div>
                    </div>

                    <div class="filter-group">
                        <label for="categoryFilter">Category</label>
                        <div class="custom-select-wrapper">
                            <div class="custom-select" id="customCategorySelect">
                                <div class="custom-select__trigger">
                                    <span id="categoryDisplay">All Categories</span>
                                    <div class="arrow"></div>
                                </div>
                                <div class="custom-options">
                                    <span class="custom-option selected" data-value="">All Categories</span>

                                    <span class="option-group-label">Programming & Development</span>
                                    <span class="custom-option" data-value="Web Development">Web Development</span>
                                    <span class="custom-option" data-value="Mobile Development">Mobile Development</span>
                                    <span class="custom-option" data-value="Full Stack">Full Stack Development</span>
                                    <span class="custom-option" data-value="Frontend">Frontend Development</span>
                                    <span class="custom-option" data-value="Backend">Backend Development</span>
                                    <span class="custom-option" data-value="Game Development">Game Development</span>
                                    <span class="custom-option" data-value="Desktop Applications">Desktop Applications</span>
                                    <span class="custom-option" data-value="E-commerce Development">E-commerce Development</span>
                                    <span class="custom-option" data-value="WordPress">WordPress Development</span>
                                    <span class="custom-option" data-value="Shopify">Shopify Development</span>
                                    <span class="custom-option" data-value="DevOps">DevOps & Infrastructure</span>
                                    <span class="custom-option" data-value="QA & Testing">QA & Testing</span>
                                    <span class="custom-option" data-value="Blockchain">Blockchain Development</span>
                                    <span class="custom-option" data-value="AR/VR">AR/VR Development</span>

                                    <span class="option-group-label">Design & Creative</span>
                                    <span class="custom-option" data-value="UI/UX">UI/UX Design</span>
                                    <span class="custom-option" data-value="Graphic Design">Graphic Design</span>
                                    <span class="custom-option" data-value="Logo Design">Logo Design</span>
                                    <span class="custom-option" data-value="Brand Design">Brand Design</span>
                                    <span class="custom-option" data-value="Web Design">Web Design</span>
                                    <span class="custom-option" data-value="Mobile App Design">Mobile App Design</span>
                                    <span class="custom-option" data-value="Illustration">Illustration</span>
                                    <span class="custom-option" data-value="Animation">Animation</span>
                                    <span class="custom-option" data-value="3D Modeling">3D Modeling & Rendering</span>
                                    <span class="custom-option" data-value="Product Design">Product Design</span>
                                    <span class="custom-option" data-value="Packaging Design">Packaging Design</span>
                                    <span class="custom-option" data-value="Print Design">Print Design</span>
                                    <span class="custom-option" data-value="Motion Graphics">Motion Graphics</span>

                                    <span class="option-group-label">Digital Marketing</span>
                                    <span class="custom-option" data-value="SEO">SEO Optimization</span>
                                    <span class="custom-option" data-value="Social Media">Social Media Marketing</span>
                                    <span class="custom-option" data-value="Content Marketing">Content Marketing</span>
                                    <span class="custom-option" data-value="Email Marketing">Email Marketing</span>
                                    <span class="custom-option" data-value="PPC">PPC & Paid Advertising</span>
                                    <span class="custom-option" data-value="SEM">Search Engine Marketing</span>
                                    <span class="custom-option" data-value="Influencer Marketing">Influencer Marketing</span>
                                    <span class="custom-option" data-value="Affiliate Marketing">Affiliate Marketing</span>
                                    <span class="custom-option" data-value="Marketing Strategy">Marketing Strategy</span>
                                    <span class="custom-option" data-value="Growth Hacking">Growth Hacking</span>
                                    <span class="custom-option" data-value="Analytics">Marketing Analytics</span>
                                    <span class="custom-option" data-value="Conversion Optimization">Conversion Optimization</span>

                                    <span class="option-group-label">Writing & Content</span>
                                    <span class="custom-option" data-value="Content Writing">Content Writing</span>
                                    <span class="custom-option" data-value="Copywriting">Copywriting</span>
                                    <span class="custom-option" data-value="Technical Writing">Technical Writing</span>
                                    <span class="custom-option" data-value="Blog Writing">Blog Writing</span>
                                    <span class="custom-option" data-value="Ghostwriting">Ghostwriting</span>
                                    <span class="custom-option" data-value="Editing">Editing & Proofreading</span>
                                    <span class="custom-option" data-value="UX Writing">UX Writing</span>
                                    <span class="custom-option" data-value="Grant Writing">Grant Writing</span>
                                    <span class="custom-option" data-value="Scriptwriting">Scriptwriting</span>
                                    <span class="custom-option" data-value="Translation">Translation</span>
                                    <span class="custom-option" data-value="Transcription">Transcription</span>

                                    <span class="option-group-label">Business & Finance</span>
                                    <span class="custom-option" data-value="Business Strategy">Business Strategy</span>
                                    <span class="custom-option" data-value="Financial Analysis">Financial Analysis</span>
                                    <span class="custom-option" data-value="Accounting">Accounting & Bookkeeping</span>
                                    <span class="custom-option" data-value="Tax Preparation">Tax Preparation</span>
                                    <span class="custom-option" data-value="Business Planning">Business Planning</span>
                                    <span class="custom-option" data-value="Market Research">Market Research</span>
                                    <span class="custom-option" data-value="Financial Modeling">Financial Modeling</span>
                                    <span class="custom-option" data-value="Investment Analysis">Investment Analysis</span>
                                    <span class="custom-option" data-value="Business Consulting">Business Consulting</span>
                                    <span class="custom-option" data-value="Legal Consulting">Legal Consulting</span>

                                    <span class="option-group-label">Sales & Customer Service</span>
                                    <span class="custom-option" data-value="Sales Strategy">Sales Strategy</span>
                                    <span class="custom-option" data-value="Lead Generation">Lead Generation</span>
                                    <span class="custom-option" data-value="CRM Management">CRM Management</span>
                                    <span class="custom-option" data-value="Customer Support">Customer Support</span>
                                    <span class="custom-option" data-value="Virtual Assistant">Virtual Assistant</span>
                                    <span class="custom-option" data-value="Telemarketing">Telemarketing</span>
                                    <span class="custom-option" data-value="Sales Funnel">Sales Funnel Optimization</span>

                                    <span class="option-group-label">Data & Analytics</span>
                                    <span class="custom-option" data-value="Data Analysis">Data Analysis</span>
                                    <span class="custom-option" data-value="Data Science">Data Science</span>
                                    <span class="custom-option" data-value="Machine Learning">Machine Learning</span>
                                    <span class="custom-option" data-value="Data Visualization">Data Visualization</span>
                                    <span class="custom-option" data-value="Business Intelligence">Business Intelligence</span>
                                    <span class="custom-option" data-value="Database Design">Database Design</span>
                                    <span class="custom-option" data-value="Big Data">Big Data</span>
                                    <span class="custom-option" data-value="Data Mining">Data Mining</span>
                                    <span class="custom-option" data-value="Data Engineering">Data Engineering</span>

                                    <span class="option-group-label">Audio & Video</span>
                                    <span class="custom-option" data-value="Video Editing">Video Editing</span>
                                    <span class="custom-option" data-value="Video Production">Video Production</span>
                                    <span class="custom-option" data-value="Audio Editing">Audio Editing</span>
                                    <span class="custom-option" data-value="Voice Over">Voice Over</span>
                                    <span class="custom-option" data-value="Music Production">Music Production</span>
                                    <span class="custom-option" data-value="Podcast Production">Podcast Production</span>
                                    <span class="custom-option" data-value="Sound Design">Sound Design</span>
                                    <span class="custom-option" data-value="Animation">Animation</span>
                                </div>
                            </div>
                            <input type="hidden" id="categoryFilter" name="categoryFilter" value="">
                        </div>
                    </div>

                    <div class="filter-group">
                        <label for="typeFilter">Job Type</label>
                        <div class="custom-select-wrapper">
                            <div class="custom-select" id="customTypeSelect">
                                <div class="custom-select__trigger">
                                    <span id="typeDisplay">All Job Types</span>
                                    <div class="arrow"></div>
                                </div>
                                <div class="custom-options">
                                    <span class="custom-option selected" data-value="">All Job Types</span>

                                    <span class="option-group-label">Employment Type</span>
                                    <span class="custom-option" data-value="Full-Time">Full Time</span>
                                    <span class="custom-option" data-value="Part-Time">Part Time</span>
                                    <span class="custom-option" data-value="Freelance">Freelance</span>
                                    <span class="custom-option" data-value="Contract">Contract</span>
                                    <span class="custom-option" data-value="Intern">Intern</span>

                                    <span class="option-group-label">Project Type</span>
                                    <span class="custom-option" data-value="One-time">One-time Project</span>
                                    <span class="custom-option" data-value="Ongoing">Ongoing Work</span>

                                    <span class="option-group-label">Payment Type</span>
                                    <span class="custom-option" data-value="Fixed-price">Fixed-price</span>
                                    <span class="custom-option" data-value="Hourly">Hourly Rate</span>
                                </div>
                            </div>
                            <input type="hidden" id="typeFilter" name="typeFilter" value="">
                        </div>
                    </div>

                    <div class="filter-group">
                        <label for="priceFilter">Min. Rate/Hour ($)</label>
                        <input type="number" id="priceFilter" class="filter-input" min="0" placeholder="Enter minimum rate">
                    </div>

                    <!-- Availability Section (Hidden by default) -->
                    <div class="filter-section">
                        <div class="filter-section-header" id="availabilityHeader">
                            <h3>Availability</h3>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="filter-section-content" id="availabilityContent" style="display: none;">
                            <div class="toggle-container">
                                <div class="toggle-switch" id="availabilityToggle">
                                    <div class="slider"></div>
                                </div>
                                <span class="toggle-label">Availability Badge <span id="toggleStatus" class="off">OFF</span></span>
                            </div>
                        </div>
                    </div>

                    <!-- Preferences Section -->
                    <div class="filter-section">
                        <div class="filter-section-header" id="preferencesHeader">
                            <h3>Preferences</h3>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="filter-section-content" id="preferencesContent">
                            <div class="filter-group">
                                <div class="visibility-header">
                                    <label>Profile Visibility</label>
                                    <a href="#" class="edit-visibility-btn" id="editVisibilityBtn">
                                        <i class="fas fa-pencil-alt"></i>
                                    </a>
                                </div>
                                <div class="visibility-pill">Public</div>
                            </div>

                            <div class="filter-group">
                                <div class="hours-header">
                                    <label>Hours per Week</label>
                                    <a href="#" class="edit-hours-btn">
                                        <i class="fas fa-pencil-alt"></i>
                                    </a>
                                </div>
                                <div class="visibility-pill">More than 20 hrs/week</div>
                            </div>
                        </div>
                    </div>

                    <!-- Proposal Section -->
                    <div class="filter-section">
                        <div class="filter-section-header" id="proposalHeader">
                            <h3>Proposal</h3>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="filter-section-content" id="proposalContent">
                            <a href="{{ url_for('my_proposal') }}" class="my-proposal-link">
                                <div class="my-proposal-item">
                                    <span>My Proposal</span>
                                    <i class="fas fa-chevron-right"></i>
                                </div>
                            </a>
                        </div>
                    </div>
                </aside>

                <!-- Jobs Container - OPTIMIZED SINGLE CARD LAYOUT -->
                <div class="jobs-container" style="width: 100%; max-width: 1200px; padding: 0; margin: 0 auto;">
                    <!-- Simplified search container -->
                    <div class="enhanced-search-container" style="margin-bottom: 1rem;">
                        <div class="search-input-wrapper" style="display: flex; border: 1px solid #e5e7eb; border-radius: 4px; overflow: hidden;">
                            <input type="text" class="enhanced-search-input" id="jobSearch" placeholder="Search for jobs, skills, or keywords..." style="flex: 1; padding: 0.75rem; border: none; outline: none;">
                            <button class="search-button" id="jobSearchBtn" style="background-color: #004AAD; color: white; border: none; padding: 0 1rem; cursor: pointer;">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>

                    <div id="noResults" class="no-results" style="display: none;">
                        No gigs match your current filters. Try adjusting your search criteria.
                    </div>

                    <!-- NEW CARD DESIGN -->
                    <div class="jobs-list" id="jobsList">
                        {% if jobs %}
                            {% for job in jobs if job.status == 'publish' %}
                            <div class="modern-job-card" data-location="{{ job.location|default('Remote') }}" data-category="{{ job.category|default('') }}" data-type="{{ job.job_type|default('') }}" data-price="{{ job.budget_amount|default(0) }}">
                                <!-- Card Header -->
                                <div class="card-header">
                                    <div class="job-title-main">{{ job.title }}</div>
                                    <div class="posted-date">Posted {{ job.created_at if job.created_at else 'Recently' }}</div>
                                </div>

                                <!-- Card Body -->
                                <div class="card-body">
                                    <!-- Client Profile Section -->
                                    <div class="client-profile-section" style="gap: 1.1rem; align-items: flex-start;">
                                        <div style="display: flex; flex-direction: column; align-items: center; min-width: 48px;">
                                            <div class="client-avatar-container">
                                                <img src="{{ url_for('api_profile_photo', user_type='client', user_id=job.client_id) }}" alt="{{ job.first_name }} {{ job.last_name }}" class="client-avatar-img">
                                            </div>
                                        </div>
                                        <div class="client-details" style="flex:1; display:flex; flex-direction:column; align-items:flex-start; gap:0.18rem;">
                                            <div style="display: flex; align-items: baseline; gap: 0.7rem;">
                                                <span class="client-name" style="font-size:1.08rem; font-weight:700; color:#222;">{{ job.first_name }} {{ job.last_name }}</span>
                                                <span class="client-business-name" style="font-size:0.97rem; color:#888; font-weight:500;">{{ job.business_name }}</span>
                                            </div>
                                            <div class="client-rating" style="margin-top:0.1rem;">
                                                <span class="rating-number">4.5</span>
                                                <div class="rating-stars" style="display:inline-block;">
                                                    {% for i in range(5) %}
                                                        <i class="fas fa-star"></i>
                                                    {% endfor %}
                                                </div>
                                            </div>
                                            {# Removed category and country from profile section #}
                                        </div>
                                    </div>

                                    <!-- Job Description -->
                                    <div class="job-description-section">
                                        {{ job.description|truncate(150) }}
                                    </div>

                                    <!-- Job Details Grid -->
                                    <div class="job-details-grid">
    {% if job.experience_level %}
    <div class="detail-item"><i class="fas fa-user-graduate"></i> <span><b>Experience:</b> {{ job.experience_level }}</span></div>
    {% endif %}
    {% if job.skills_list and job.skills_list|length > 0 %}
    <div class="detail-item skills-item"><i class="fas fa-tools"></i> 
        <span><b>Skills:</b> 
            {% set max_skills = 3 %}
            {% for skill in job.skills_list[:max_skills] %}
                {{ skill }}{% if not loop.last %}, {% endif %}
            {% endfor %}
            {% if job.skills_list|length > max_skills %}
                <span class="skills-more">+{{ job.skills_list|length - max_skills }} more</span>
            {% endif %}
        </span>
    </div>
    {% endif %}
    {% if job.country %}
    <div class="detail-item"><i class="fas fa-map-marker-alt"></i> <span><b>Country:</b> {{ job.country }}</span></div>
    {% endif %}
    {% if job.category %}
    <div class="detail-item"><i class="fas fa-tags"></i> <span><b>Category:</b> {{ job.category }}</span></div>
    {% endif %}
    {% if job.specialty %}
    <div class="detail-item"><i class="fas fa-star"></i> <span><b>Specialty:</b> {{ job.specialty }}</span></div>
    {% endif %}
    {% if job.budget_type and job.budget_amount %}
    <div class="detail-item"><i class="fas fa-dollar-sign"></i>
        <span><b>Budget:</b>
            {% if job.budget_type == 'fixed' %}
                ${{ job.budget_amount }} (Fixed Price)
            {% elif job.budget_type == 'hourly' %}
                ${{ job.budget_amount }}/hour
            {% else %}
                ${{ job.budget_amount }}
            {% endif %}
        </span>
    </div>
    {% endif %}
</div>

                                    <!-- Card Footer -->
                                    <div class="card-footer">
                                        <div class="footer-info">
                                            <small><i class="fas fa-info-circle"></i> View details to see all details</small>
                                        </div>
                                        <button class="view-details-btn" onclick="openJobDetailsModal({{ job.id }})">
                                            <i class="fas fa-eye"></i> View Details
                                        </button>
                                    </div>
                                </div>

                            {% endfor %}
                            {% else %}
                            <div class="no-jobs">
                            <h2>No jobs available at the moment</h2>
                            <p>Check back later for new opportunities or adjust your search filters.</p>
                            </div>
                            {% endif %}
                            <!-- End jobs-list -->
                            </div>
                            <!-- Pagination Controls -->
                            {% if pagination and pagination.total_pages > 1 %}
                            <div class="pagination-container">
                            <div class="pagination">
                            {% if pagination.has_prev %}
                            <a href="{{ url_for('genius_page', page=pagination.page-1) }}" class="pagination-btn prev">
                            <i class="fas fa-chevron-left"></i> Previous
                            </a>
                            {% else %}
                            <span class="pagination-btn prev disabled">
                            <i class="fas fa-chevron-left"></i> Previous
                            </span>
                            {% endif %}
                            
                            <div class="pagination-numbers">
                            {% set start_page = [1, pagination.page - 2]|max %}
                            {% set end_page = [pagination.total_pages, start_page + 4]|min %}
                            {% set start_page = [1, end_page - 4]|max %}
                            
                            {% if start_page > 1 %}
                            <a href="{{ url_for('genius_page', page=1) }}" class="pagination-number">1</a>
                            {% if start_page > 2 %}
                            <span class="pagination-ellipsis">...</span>
                            {% endif %}
                            {% endif %}
                            
                            {% for p in range(start_page, end_page + 1) %}
                            {% if p == pagination.page %}
                            <span class="pagination-number active">{{ p }}</span>
                            {% else %}
                            <a href="{{ url_for('genius_page', page=p) }}" class="pagination-number">{{ p }}</a>
                            {% endif %}
                            {% endfor %}
                            
                            {% if end_page < pagination.total_pages %}
                            {% if end_page < pagination.total_pages - 1 %}
                            <span class="pagination-ellipsis">...</span>
                            {% endif %}
                            <a href="{{ url_for('genius_page', page=pagination.total_pages) }}" class="pagination-number">{{ pagination.total_pages }}</a>
                            {% endif %}
                            </div>
                            
                            {% if pagination.has_next %}
                            <a href="{{ url_for('genius_page', page=pagination.page+1) }}" class="pagination-btn next">
                            Next <i class="fas fa-chevron-right"></i>
                            </a>
                            {% else %}
                            <span class="pagination-btn next disabled">
                            Next <i class="fas fa-chevron-right"></i>
                            </span>
                            {% endif %}
                            </div>
                            <!-- Simplified pagination info -->
                            <div class="pagination-info">
                            Page {{ pagination.page }} of {{ pagination.total_pages }}
                            </div>
                            </div>
                            {% endif %}
                            </div>
                            <!-- End jobs-container -->
                            </div>
                            <!-- End dashboard-content -->
                            </div>
                            <!-- End dashboardContent -->
                            
                            <script>
        // Enhanced profile dropdown functionality
        window.addEventListener('load', function() {
            const profileDropdown = document.querySelector('.profile-dropdown');
            const profileButton = profileDropdown?.querySelector('.profile-button');
            const dropdownContent = profileDropdown?.querySelector('.profile-dropdown-content');

            if (profileButton && profileDropdown && dropdownContent) {
                // Toggle dropdown on profile button click
                profileButton.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const isActive = profileDropdown.classList.contains('active');

                    // Close any other open dropdowns
                    document.querySelectorAll('.profile-dropdown.active').forEach(dropdown => {
                        if (dropdown !== profileDropdown) {
                            dropdown.classList.remove('active');
                        }
                    });

                    profileDropdown.classList.toggle('active');

                    // Focus first menu item when opening
                    if (!isActive) {
                        setTimeout(() => {
                            const firstLink = dropdownContent.querySelector('a');
                            if (firstLink) firstLink.focus();
                        }, 100);
                    }

                    console.log('Profile dropdown clicked, active:', profileDropdown.classList.contains('active'));
                });

                // Close dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (!profileDropdown.contains(e.target)) {
                        profileDropdown.classList.remove('active');
                    }
                });

                // Close dropdown with Escape key
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Escape' && profileDropdown.classList.contains('active')) {
                        profileDropdown.classList.remove('active');
                        profileButton.focus();
                    }
                });

                // Keyboard navigation within dropdown
                dropdownContent.addEventListener('keydown', function(e) {
                    const links = Array.from(dropdownContent.querySelectorAll('a'));
                    const currentIndex = links.indexOf(document.activeElement);

                    if (e.key === 'ArrowDown') {
                        e.preventDefault();
                        const nextIndex = (currentIndex + 1) % links.length;
                        links[nextIndex].focus();
                    } else if (e.key === 'ArrowUp') {
                        e.preventDefault();
                        const prevIndex = currentIndex === 0 ? links.length - 1 : currentIndex - 1;
                        links[prevIndex].focus();
                    } else if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        if (document.activeElement) {
                            document.activeElement.click();
                        }
                    }
                });
            }
        });

        // Prevent back button navigation to landing page
        window.addEventListener('pageshow', function(event) {
            // If the page is loaded from the browser cache (back button)
            if (event.persisted) {
                // Reload the page to ensure proper session state
                window.location.reload();
            }
        });

        // Disable browser cache for this page
        window.onload = function() {
            // Set cache control headers via meta tags
            document.head.innerHTML += '<meta http-equiv="Cache-Control" content="no-store, no-cache, must-revalidate, post-check=0, pre-check=0, max-age=0">';
            document.head.innerHTML += '<meta http-equiv="Pragma" content="no-cache">';
            document.head.innerHTML += '<meta http-equiv="Expires" content="-1">';
        };

        // Quote Slider Functionality
        let currentQuoteIndex = 0;
        const quotes = [
            {
                main: "Getting a job shouldn't be expensive.",
                sub: "No connect or bids needed."
            },
            {
                main: "Find your dream job with ease.",
                sub: "Personalized job matches for your skills."
            },
            {
                main: "Work with clients who value your expertise.",
                sub: "Quality over quantity, always."
            }
        ];

        function changeQuote(direction) {
            const quoteElement = document.getElementById('currentQuote');
            quoteElement.classList.add('fade');

            setTimeout(() => {
                currentQuoteIndex = (currentQuoteIndex + direction + quotes.length) % quotes.length;
                updateQuote();
                updateDots();
                quoteElement.classList.remove('fade');
            }, 300);
        }

        function jumpToQuote(index) {
            if (index === currentQuoteIndex) return;

            const quoteElement = document.getElementById('currentQuote');
            quoteElement.classList.add('fade');

            setTimeout(() => {
                currentQuoteIndex = index;
                updateQuote();
                updateDots();
                quoteElement.classList.remove('fade');
            }, 300);
        }

        function updateQuote() {
            const quoteElement = document.getElementById('currentQuote');
            const quote = quotes[currentQuoteIndex];
            quoteElement.innerHTML = `
                <span class="main-text">${quote.main}</span>
                <span class="sub-text">${quote.sub}</span>
            `;
        }

        function updateDots() {
            const dots = document.querySelectorAll('.quote-dot');
            dots.forEach((dot, index) => {
                if (index === currentQuoteIndex) {
                    dot.classList.add('active');
                } else {
                    dot.classList.remove('active');
                }
            });
        }

        // Auto-rotate quotes every 5 seconds
        setInterval(() => {
            changeQuote(1);
        }, 5000);

        // My Applications Modal functionality
        function openMyApplicationsModal() {
            const modal = document.getElementById('myApplicationsModal');
            modal.style.display = 'block';
            loadApplications();
        }

        function closeApplicationsModal() {
            const modal = document.getElementById('myApplicationsModal');
            modal.style.display = 'none';
        }

        function loadApplications() {
            const container = document.getElementById('applicationsContainer');

            // Show loading state
            container.innerHTML = `
                <div class="loading-applications">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>Loading your applications...</p>
                </div>
            `;

            // Fetch applications from the server
            fetch('/my_application')
                .then(response => response.json())
                .then(data => {
                    if (data.applications && data.applications.length > 0) {
                        displayApplications(data.applications);
                    } else {
                        displayNoApplications();
                    }
                })
                .catch(error => {
                    console.error('Error loading applications:', error);
                    displayErrorMessage();
                });
        }

        function displayApplications(applications) {
            const container = document.getElementById('applicationsContainer');
            let html = '';

            // Sort applications to show accepted ones first
            applications.sort((a, b) => {
                const statusPriority = {
                    'accepted': 1,
                    'accept': 1,
                    'pending': 2,
                    'rejected': 3,
                    'withdraw': 4
                };
                return (statusPriority[a.status.toLowerCase()] || 5) - (statusPriority[b.status.toLowerCase()] || 5);
            });

            applications.forEach(app => {
                const statusClass = `status-${app.status.toLowerCase().replace(' ', '-')}`;
                const isAccepted = app.status.toLowerCase() === 'accepted' || app.status.toLowerCase() === 'accept';

                html += `
                    <div class="application-item ${isAccepted ? 'application-accepted' : ''}">
                        <div class="application-header">
                            <div>
                                <div class="application-title">
                                    ${isAccepted ? '<i class="fas fa-check-circle text-success me-2"></i>' : ''}
                                    ${app.job_title}
                                </div>
                                <div class="application-company">${app.company_name || 'Company Name'}</div>
                            </div>
                            <div class="application-status ${statusClass}">
                                ${isAccepted ? '<i class="fas fa-star me-1"></i>' : ''}
                                ${app.status}
                            </div>
                        </div>
                        <div class="application-details">
                            <div class="application-detail">
                                <span class="detail-label">Location</span>
                                <span class="detail-value">${app.location || 'Remote'}</span>
                            </div>
                            <div class="application-detail">
                                <span class="detail-label">Proposed Rate</span>
                                <span class="detail-value">$${app.proposed_rate}/hr</span>
                            </div>
                            <div class="application-detail">
                                <span class="detail-label">Applied Date</span>
                                <span class="detail-value">${new Date(app.applied_date).toLocaleDateString()}</span>
                            </div>
                            <div class="application-detail">
                                <span class="detail-label">Job Type</span>
                                <span class="detail-value">${app.job_type || 'Full-time'}</span>
                            </div>
                        </div>
                        <div class="application-description">
                            ${app.job_description ? app.job_description.substring(0, 150) + '...' : 'No description available'}
                        </div>
                        <div class="application-actions">
                            <button class="action-btn btn-view" onclick="viewJobDetails(${app.job_id})">
                                <i class="fas fa-eye"></i> View Job
                            </button>
                            ${app.status.toLowerCase() === 'pending' ?
                                `<button class="action-btn btn-withdraw" onclick="withdrawApplication(${app.id}, '${app.job_title.replace(/'/g, "\\'")}')">
                                    <i class="fas fa-times"></i> Withdraw
                                </button>` : ''
                            }
                            ${isAccepted ?
                                `<button class="action-btn btn-tracker" onclick="goToTracker()">
                                    <i class="fas fa-clock"></i> Track Time
                                </button>` : ''
                            }
                        </div>
                        ${isAccepted ?
                            `<div class="acceptance-notice">
                                <i class="fas fa-party-horn"></i>
                                <strong>Congratulations!</strong> Your application has been accepted. You can now track your work time for this project.
                            </div>` : ''
                        }
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        // Function to navigate to tracker
        function goToTracker() {
            window.location.href = '/tracker';
        }

        function displayNoApplications() {
            const container = document.getElementById('applicationsContainer');
            container.innerHTML = `
                <div class="no-applications">
                    <i class="fas fa-folder-open"></i>
                    <h3>No Applications Yet</h3>
                    <p>You haven't applied to any jobs yet. Start browsing available gigs to find your next opportunity!</p>
                </div>
            `;
        }

        function displayErrorMessage() {
            const container = document.getElementById('applicationsContainer');
            container.innerHTML = `
                <div class="no-applications">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h3>Error Loading Applications</h3>
                    <p>There was an error loading your applications. Please try again later.</p>
                </div>
            `;
        }

        function viewJobDetails(jobId) {
            // Redirect to job details page or open job details modal
            window.location.href = `/job/${jobId}`;
        }

        // Job Details Modal functionality
        function openJobDetailsModal(jobId) {
            const modal = document.getElementById('jobDetailsModal');
            const container = document.getElementById('jobDetailsContainer');

            // Show modal
            modal.style.display = 'block';

            // Show loading state
            container.innerHTML = `
                <div class="loading-job-details">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>Loading job details...</p>
                </div>
            `;

            // Fetch job details from API
            fetch(`/job_details_api/${jobId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayJobDetails(data.job, data.client);
                    } else {
                        container.innerHTML = `
                            <div class="error-job-details">
                                <i class="fas fa-exclamation-triangle"></i>
                                <p>Error loading job details: ${data.error || 'Unknown error'}</p>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('Error fetching job details:', error);
                    container.innerHTML = `
                        <div class="error-job-details">
                            <i class="fas fa-exclamation-triangle"></i>
                            <p>Error loading job details. Please try again later.</p>
                        </div>
                    `;
                });
        }

        function closeJobDetailsModal() {
            const modal = document.getElementById('jobDetailsModal');
            modal.style.display = 'none';
        }

        function applyForJob(jobId) {
            // First check the global cache (fastest method)
            if (userAppliedJobIds.has(parseInt(jobId))) {
                updateApplyButtonToApplied(jobId, 'pending');
                return;
            }

            // If not in cache, check via API before showing modal
            fetch(`/check_application_status/${jobId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.has_applied) {
                        // Add to cache and hide button
                        userAppliedJobIds.add(parseInt(jobId));
                        updateApplyButtonToApplied(jobId, data.status);
                        return;
                    }

                    // User hasn't applied, proceed with normal flow
                    const jobTitle = document.getElementById('jobDetailsTitle').textContent;

                    // Show confirmation modal
                    showApplicationConfirmModal(jobId, jobTitle);
                })
                .catch(error => {
                    console.error('Error in quick application check:', error);
                    // If check fails, proceed with normal flow
                    const jobTitle = document.getElementById('jobDetailsTitle').textContent;

                    // Show confirmation modal
                    showApplicationConfirmModal(jobId, jobTitle);
                });
        }

        function showApplicationConfirmModal(jobId, jobTitle) {
            const modal = document.getElementById('applicationConfirmModal');
            const confirmJobTitleElement = document.getElementById('confirmJobTitle');
            const confirmApplyBtn = document.getElementById('confirmApplyBtn');

            // Set job title with professional formatting
            confirmJobTitleElement.textContent = jobTitle;

            // Reset button state
            confirmApplyBtn.innerHTML = '<i class="fas fa-paper-plane"></i> Yes, Apply Now';
            confirmApplyBtn.disabled = false;
            confirmApplyBtn.style.background = '';

            // Set up the confirm button click handler
            confirmApplyBtn.onclick = function() {
                submitJobApplication(jobId);
            };

            // Show modal with smooth animation
            modal.style.display = 'block';

            // Add entrance animation
            setTimeout(() => {
                modal.style.opacity = '1';
                const modalContent = modal.querySelector('.modal-content');
                modalContent.style.transform = 'scale(1)';
            }, 10);
        }

        function closeApplicationConfirmModal() {
            const modal = document.getElementById('applicationConfirmModal');
            const modalContent = modal.querySelector('.modal-content');

            // Add exit animation
            modal.style.opacity = '0';
            modalContent.style.transform = 'scale(0.95)';

            // Hide modal after animation
            setTimeout(() => {
                modal.style.display = 'none';
            }, 200);
        }

        function submitJobApplication(jobId) {
            const confirmApplyBtn = document.getElementById('confirmApplyBtn');

            // Show loading state
            confirmApplyBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Applying...';
            confirmApplyBtn.disabled = true;

            // Send AJAX request to apply to the job
            fetch(`/apply_to_job/${jobId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success state
                    confirmApplyBtn.innerHTML = '<i class="fas fa-check"></i> Applied Successfully!';
                    confirmApplyBtn.style.background = '#10b981';

                    // Show success notification
                    showSystemNotification('Application submitted successfully! The client will be notified.', 'success');

                    // Add to cache and hide the apply button
                    userAppliedJobIds.add(parseInt(jobId));
                    updateApplyButtonToApplied(jobId, 'pending');

                    // Close modals after a short delay
                    setTimeout(() => {
                        closeApplicationConfirmModal();
                        closeJobDetailsModal();
                    }, 1500);
                } else {
                    // Show error state
                    confirmApplyBtn.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Error';
                    confirmApplyBtn.style.background = '#ef4444';
                    showSystemNotification('Error applying to job: ' + (data.error || 'Unknown error'), 'error');

                    // Reset button after delay
                    setTimeout(() => {
                        confirmApplyBtn.innerHTML = '<i class="fas fa-paper-plane"></i> Yes, Apply Now';
                        confirmApplyBtn.style.background = '';
                        confirmApplyBtn.disabled = false;
                    }, 2000);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                confirmApplyBtn.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Error';
                confirmApplyBtn.style.background = '#ef4444';
                showSystemNotification('Error applying to job. Please try again.', 'error');

                // Reset button after delay
                setTimeout(() => {
                    confirmApplyBtn.innerHTML = '<i class="fas fa-paper-plane"></i> Yes, Apply Now';
                    confirmApplyBtn.style.background = '';
                    confirmApplyBtn.disabled = false;
                }, 2000);
            });
        }

        function displayJobDetails(job, client) {
            const container = document.getElementById('jobDetailsContainer');
            const titleElement = document.getElementById('jobDetailsTitle');

            // Update modal title
            titleElement.textContent = job.title;

            // Format skills
            const skills = job.skills ? job.skills.split(',').map(skill => skill.trim()) : [];
            const skillsHtml = skills.length > 0 ?
                skills.map(skill => `<span class="skill-tag-modal">${skill}</span>`).join('') :
                '<span class="no-skills">No specific skills listed</span>';

            // Format budget
            let budgetDisplay = 'Not specified';
            if (job.budget_type && job.budget_amount) {
                if (job.budget_type === 'fixed') {
                    budgetDisplay = `$${job.budget_amount} (Fixed Price)`;
                } else if (job.budget_type === 'hourly') {
                    budgetDisplay = `$${job.budget_amount}/hour`;
                }
            }

            // Format date
            const createdDate = job.created_at ? new Date(job.created_at).toLocaleDateString() : 'Not available';

            container.innerHTML = `
                <div class="job-details-content">
                    <!-- Client Information -->
                    <div class="client-info-modal">
                        <div class="client-avatar-modal">
                            <img src="/api/profile-photo/client/${client.id}" alt="${client.first_name} ${client.last_name}"
                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <div class="client-avatar-fallback" style="display:none; width:60px; height:60px; background:#f0f0f0; border-radius:50%; align-items:center; justify-content:center;">
                                <i class="fas fa-building" style="color:#666; font-size:24px;"></i>
                            </div>
                        </div>
                        <div class="client-details-modal">
                            <div class="client-name-modal">${client.business_name || (client.first_name + ' ' + client.last_name)}</div>
                            <div class="client-rating-modal">
                                <i class="fas fa-star"></i>
                                <span>${client.rating || 'No rating'}</span>
                                <span class="client-location">${client.country || 'Location not specified'}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Job Information -->
                    <div class="job-info-section">
                        <h4><i class="fas fa-info-circle"></i> Job Information</h4>
                        <div class="job-info-grid">
                            <div class="info-item">
                                <label>Category:</label>
                                <span>${job.category || 'Not specified'}</span>
                            </div>
                            <div class="info-item">
                                <label>Specialty:</label>
                                <span>${job.specialty || 'Not specified'}</span>
                            </div>
                            <div class="info-item">
                                <label>Job Type:</label>
                                <span>${job.job_type || 'Not specified'}</span>
                            </div>
                            <div class="info-item">
                                <label>Project Size:</label>
                                <span>${job.project_size || 'Not specified'}</span>
                            </div>
                            <div class="info-item">
                                <label>Duration:</label>
                                <span>${job.duration || 'Not specified'}</span>
                            </div>
                            <div class="info-item">
                                <label>Experience Level:</label>
                                <span>${job.experience_level || 'Not specified'}</span>
                            </div>
                            <div class="info-item">
                                <label>Budget:</label>
                                <span>${budgetDisplay}</span>
                            </div>
                            <div class="info-item">
                                <label>Posted:</label>
                                <span>${createdDate}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Job Description -->
                    <div class="job-description-section">
                        <h4><i class="fas fa-file-alt"></i> Job Description</h4>
                        <div class="job-description-content">
                            ${job.description || 'No description provided'}
                        </div>
                    </div>

                    <!-- Project Description -->
                    ${job.project_description ? `
                        <div class="project-description-section">
                            <h4><i class="fas fa-project-diagram"></i> Project Details</h4>
                            <div class="project-description-content">
                                ${job.project_description}
                            </div>
                        </div>
                    ` : ''}

                    <!-- Skills -->
                    <div class="skills-section">
                        <h4><i class="fas fa-tools"></i> Required Skills</h4>
                        <div class="skills-container">
                            ${skillsHtml}
                        </div>
                    </div>

                    <!-- Apply Button -->
                    <div class="modal-actions">
                        <button class="apply-btn" id="applyButton-${job.id}" onclick="applyForJob(${job.id})">
                            <i class="fas fa-paper-plane"></i> Apply for this Job
                        </button>
                    </div>
                </div>
            `;

            // Check if user has already applied to this job - do this immediately
            setTimeout(() => {
                checkApplicationStatus(job.id);
            }, 100);
        }

        function checkApplicationStatus(jobId) {
            // First check the global variable (fastest method)
            if (userAppliedJobIds.has(parseInt(jobId))) {
                updateApplyButtonToApplied(jobId, 'pending');
                return;
            }

            // If not in cache, try the dedicated endpoint
            fetch(`/check_application_status/${jobId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.has_applied) {
                        // Add to cache
                        userAppliedJobIds.add(parseInt(jobId));
                        updateApplyButtonToApplied(jobId, data.status);
                    }
                })
                .catch(error => {
                    console.error('Error checking application status:', error);
                    // Fallback: check using the my_application endpoint
                    checkApplicationStatusFallback(jobId);
                });
        }

        function checkApplicationStatusFallback(jobId) {
            fetch('/my_application')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.applications) {
                        // Check if any application matches this job ID
                        const hasApplied = data.applications.some(app => app.job_id == jobId);
                        if (hasApplied) {
                            const application = data.applications.find(app => app.job_id == jobId);
                            // Add to cache
                            userAppliedJobIds.add(parseInt(jobId));
                            updateApplyButtonToApplied(jobId, application.status || 'pending');
                        }
                    }
                })
                .catch(error => {
                    console.error('Error in fallback application status check:', error);
                });
        }

        function updateApplyButtonToApplied(jobId, status) {
            const applyBtn = document.getElementById(`applyButton-${jobId}`);

            if (applyBtn) {
                // Hide the apply button completely since user has already applied
                applyBtn.style.display = 'none';

                // Create and show an "Already Applied" message instead
                const modalActions = applyBtn.parentElement;

                // Check if already applied message exists
                let appliedMessage = modalActions.querySelector('.already-applied-message');
                if (!appliedMessage) {
                    appliedMessage = document.createElement('div');
                    appliedMessage.className = 'already-applied-message';
                    appliedMessage.innerHTML = `
                        <div class="applied-status-container">
                            <div class="applied-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="applied-text">
                                <h4>Application Submitted</h4>
                                <p>You have already applied to this job</p>
                                <span class="status-badge status-${status.toLowerCase()}">${status.charAt(0).toUpperCase() + status.slice(1)}</span>
                            </div>
                        </div>
                    `;
                    modalActions.appendChild(appliedMessage);
                }
                appliedMessage.style.display = 'block';
            }
        }

        function withdrawApplication(applicationId, jobTitle) {
            // Show withdraw confirmation modal
            showWithdrawConfirmModal(applicationId, jobTitle);
        }

        function showWithdrawConfirmModal(applicationId, jobTitle) {
            const modal = document.getElementById('withdrawConfirmModal');
            const withdrawJobTitleElement = document.getElementById('withdrawJobTitle');
            const confirmWithdrawBtn = document.getElementById('confirmWithdrawBtn');

            // Set job title with professional formatting
            withdrawJobTitleElement.textContent = jobTitle;

            // Reset button state
            confirmWithdrawBtn.innerHTML = '<i class="fas fa-trash"></i> Yes, Withdraw';
            confirmWithdrawBtn.disabled = false;
            confirmWithdrawBtn.style.background = '';

            // Set up the confirm button click handler
            confirmWithdrawBtn.onclick = function() {
                submitWithdrawApplication(applicationId);
            };

            // Show modal with smooth animation
            modal.style.display = 'block';

            // Add entrance animation
            setTimeout(() => {
                modal.style.opacity = '1';
                const modalContent = modal.querySelector('.modal-content');
                modalContent.style.transform = 'scale(1)';
            }, 10);
        }

        function closeWithdrawConfirmModal() {
            const modal = document.getElementById('withdrawConfirmModal');
            const modalContent = modal.querySelector('.modal-content');

            // Add exit animation
            modal.style.opacity = '0';
            modalContent.style.transform = 'scale(0.95)';

            // Hide modal after animation
            setTimeout(() => {
                modal.style.display = 'none';
            }, 200);
        }

        function submitWithdrawApplication(applicationId) {
            const confirmWithdrawBtn = document.getElementById('confirmWithdrawBtn');

            // Show loading state
            confirmWithdrawBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Withdrawing...';
            confirmWithdrawBtn.disabled = true;

            fetch(`/withdraw_application/${applicationId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success state
                    confirmWithdrawBtn.innerHTML = '<i class="fas fa-check"></i> Withdrawn Successfully!';
                    confirmWithdrawBtn.style.background = '#10b981';

                    // Show success notification
                    showSystemNotification('Application withdrawn successfully.', 'success');

                    // Close modal and reload applications after a short delay
                    setTimeout(() => {
                        closeWithdrawConfirmModal();
                        loadApplications(); // Reload applications

                        // Reload user applications cache
                        loadUserApplicationsForButtonCheck();

                        // If job details modal is open, restore the apply button
                        restoreApplyButtonAfterWithdraw();
                    }, 1500);
                } else {
                    // Show error state
                    confirmWithdrawBtn.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Error';
                    confirmWithdrawBtn.style.background = '#ef4444';
                    showSystemNotification('Error withdrawing application: ' + (data.error || 'Please try again.'), 'error');

                    // Reset button after delay
                    setTimeout(() => {
                        confirmWithdrawBtn.innerHTML = '<i class="fas fa-trash"></i> Yes, Withdraw';
                        confirmWithdrawBtn.style.background = '';
                        confirmWithdrawBtn.disabled = false;
                    }, 2000);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                confirmWithdrawBtn.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Error';
                confirmWithdrawBtn.style.background = '#ef4444';
                showSystemNotification('Error withdrawing application. Please try again.', 'error');

                // Reset button after delay
                setTimeout(() => {
                    confirmWithdrawBtn.innerHTML = '<i class="fas fa-trash"></i> Yes, Withdraw';
                    confirmWithdrawBtn.style.background = '';
                    confirmWithdrawBtn.disabled = false;
                }, 2000);
            });
        }

        function restoreApplyButtonAfterWithdraw() {
            // Check if job details modal is open
            const jobDetailsModal = document.getElementById('jobDetailsModal');
            if (jobDetailsModal && jobDetailsModal.style.display === 'block') {
                // Find any hidden apply buttons and restore them
                const hiddenApplyBtn = jobDetailsModal.querySelector('.apply-btn[style*="display: none"]');
                const appliedMessage = jobDetailsModal.querySelector('.already-applied-message');

                if (hiddenApplyBtn && appliedMessage) {
                    // Show the apply button again
                    hiddenApplyBtn.style.display = 'inline-flex';

                    // Hide the applied message
                    appliedMessage.style.display = 'none';
                }
            }
        }

        // Header functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Mobile menu functionality
            const mobileMenuBtn = document.getElementById('mobileMenuBtn');
            const mobileMenu = document.getElementById('mobileMenu');
            const mobileCloseBtn = document.getElementById('mobileCloseBtn');

            if (mobileMenuBtn && mobileMenu) {
                mobileMenuBtn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    mobileMenu.classList.add('active');
                    document.body.style.overflow = 'hidden';
                });
            }

            if (mobileCloseBtn && mobileMenu) {
                mobileCloseBtn.addEventListener('click', function() {
                    mobileMenu.classList.remove('active');
                    document.body.style.overflow = '';
                });
            }

            // Close mobile menu when clicking outside
            if (mobileMenu) {
                mobileMenu.addEventListener('click', function(e) {
                    if (e.target === mobileMenu) {
                        mobileMenu.classList.remove('active');
                        document.body.style.overflow = '';
                    }
                });
            }

            // Simple search functionality
            const searchInput = document.getElementById('searchInput');

            if (searchInput) {
                // Focus search input when user starts typing (optional)
                document.addEventListener('keydown', function(e) {
                    // Focus search when user types and no input is focused
                    if (!document.activeElement || document.activeElement.tagName !== 'INPUT') {
                        if (e.key.length === 1 && !e.ctrlKey && !e.metaKey && !e.altKey) {
                            searchInput.focus();
                        }
                    }
                });
            }






        });

        // Unified Notification System - Exact copy from client_page.html
        function showUnifiedNotification(options) {
            const {
                message,
                type = 'info',
                title = null,
                duration = 3000,
                clickAction = null,
                position = 'bottom-right',
                showClose = true,
                persistent = false
            } = options;

            const notification = document.createElement('div');
            notification.className = `unified-notification ${type}`;

            // Add icon based on type
            let icon = '';
            switch(type) {
                case 'success':
                    icon = '<i class="fas fa-check-circle"></i>';
                    break;
                case 'error':
                    icon = '<i class="fas fa-exclamation-circle"></i>';
                    break;
                case 'warning':
                    icon = '<i class="fas fa-exclamation-triangle"></i>';
                    break;
                case 'info':
                    icon = '<i class="fas fa-info-circle"></i>';
                    break;
                case 'application':
                    icon = '<i class="fas fa-user-plus"></i>';
                    break;
                case 'contract':
                    icon = '<i class="fas fa-file-contract"></i>';
                    break;
                default:
                    icon = '<i class="fas fa-bell"></i>';
            }

            // Build notification content
            let content = `
                <div class="notification-icon">${icon}</div>
                <div class="notification-content">
                    ${title ? `<div class="notification-title">${title}</div>` : ''}
                    <div class="notification-message">${message}</div>
                </div>
            `;

            if (showClose) {
                content += `<button class="notification-close" onclick="this.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>`;
            }

            notification.innerHTML = content;

            // Add click action if provided
            if (clickAction) {
                notification.style.cursor = 'pointer';
                notification.addEventListener('click', function(e) {
                    if (!e.target.closest('.notification-close')) {
                        clickAction();
                        notification.remove();
                    }
                });
            }

            // Position the notification
            setNotificationPosition(notification, position);

            document.body.appendChild(notification);

            // Show the notification
            setTimeout(() => {
                notification.classList.add('show');
            }, 10);

            // Auto-hide if not persistent
            if (!persistent && duration > 0) {
                setTimeout(() => {
                    notification.classList.remove('show');
                    setTimeout(() => {
                        if (notification.parentElement) {
                            notification.remove();
                        }
                    }, 300);
                }, duration);
            }

            return notification;
        }

        function setNotificationPosition(notification, position) {
            // Reset all position styles
            notification.style.top = 'auto';
            notification.style.bottom = 'auto';
            notification.style.left = 'auto';
            notification.style.right = 'auto';

            switch(position) {
                case 'top-right':
                    notification.style.top = '30px';
                    notification.style.right = '30px';
                    break;
                case 'top-left':
                    notification.style.top = '30px';
                    notification.style.left = '30px';
                    break;
                case 'bottom-left':
                    notification.style.bottom = '30px';
                    notification.style.left = '30px';
                    break;
                case 'center':
                    notification.style.top = '50%';
                    notification.style.left = '50%';
                    notification.style.transform = 'translate(-50%, -50%)';
                    break;
                default: // bottom-right
                    notification.style.bottom = '30px';
                    notification.style.right = '30px';
            }
        }

        function showSystemNotification(message, type = 'info') {
            return showUnifiedNotification({
                message: message,
                type: type,
                duration: 3000
            });
        }

        // Real-time notification badge management
        let notificationCount = 0;

        function updateNotificationBadge(count = null) {
            const badge = document.getElementById('notification-count');
            const bell = document.getElementById('notification-bell');

            if (count !== null) {
                notificationCount = count;
            }

            if (badge) {
                if (notificationCount > 0) {
                    badge.textContent = notificationCount > 99 ? '99+' : notificationCount;
                    badge.style.display = 'flex';
                    badge.classList.add('new');

                    // Remove animation class after animation completes
                    setTimeout(() => {
                        badge.classList.remove('new');
                    }, 600);
                } else {
                    badge.style.display = 'none';
                }
            }

            // Add bell shake animation for new notifications
            if (bell && count > 0) {
                bell.style.animation = 'shake 0.5s ease-in-out';
                setTimeout(() => {
                    bell.style.animation = '';
                }, 500);
            }
        }

        function incrementNotificationBadge() {
            notificationCount++;
            updateNotificationBadge();
        }

        function decrementNotificationBadge() {
            if (notificationCount > 0) {
                notificationCount--;
                updateNotificationBadge();
            }
        }

        function resetNotificationBadge() {
            notificationCount = 0;
            updateNotificationBadge();
        }

        // Simple search functionality
        function performSearch() {
            const searchInput = document.getElementById('searchInput');

            if (searchInput && searchInput.value.trim()) {
                const query = searchInput.value.trim();

                console.log(`Searching for: "${query}"`);
                // Add your search logic here
                // For example, you could redirect to a search results page:
                // window.location.href = `/search?q=${encodeURIComponent(query)}`;

                // Or filter existing content on the page
                // filterContent(query);
            }
        }
        document.addEventListener('DOMContentLoaded', function() {
            // Close dropdowns when clicking outside
            window.addEventListener('click', function(e) {
                if (!e.target.matches('.nav-dropbtn')) {
                    const dropdowns = document.getElementsByClassName('nav-dropdown-content');
                    for (let dropdown of dropdowns) {
                        if (dropdown.classList.contains('show')) {
                            dropdown.classList.remove('show');
                        }
                    }
                }
            });
        });


       

        
        // Job filtering functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Get URL parameters for filters
            const urlParams = new URLSearchParams(window.location.search);
            const locationParam = urlParams.get('location') || '';
            const categoryParam = urlParams.get('category') || '';
            const typeParam = urlParams.get('type') || '';
            const priceParam = urlParams.get('price') || '';
            const searchParam = urlParams.get('search') || '';

            // Set search input value from URL parameter
            if (searchParam) {
                document.getElementById('jobSearch').value = searchParam;
            }

            // Set price filter value from URL parameter
            if (priceParam) {
                document.getElementById('priceFilter').value = priceParam;
            }

            // My Applications Modal Event Listeners
            const myApplicationsLink = document.getElementById('myApplicationsLink');
            const mobileMyApplicationsLink = document.getElementById('mobileMyApplicationsLink');
            const myApplicationsBtn = document.getElementById('myApplicationsBtn');
            const closeMyApplicationsModal = document.getElementById('closeMyApplicationsModal');
            const myApplicationsModal = document.getElementById('myApplicationsModal');

            if (myApplicationsLink) {
                myApplicationsLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    openMyApplicationsModal();
                });
            }

            if (mobileMyApplicationsLink) {
                mobileMyApplicationsLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    openMyApplicationsModal();
                    // Close mobile menu if open
                    const mobileMenu = document.getElementById('mobileMenu');
                    if (mobileMenu) {
                        mobileMenu.classList.remove('active');
                    }
                });
            }

            if (myApplicationsBtn) {
                myApplicationsBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    openMyApplicationsModal();
                });
            }

            if (closeMyApplicationsModal) {
                closeMyApplicationsModal.addEventListener('click', function() {
                    closeApplicationsModal();
                });
            }

            // Close modal when clicking outside
            if (myApplicationsModal) {
                myApplicationsModal.addEventListener('click', function(e) {
                    if (e.target === myApplicationsModal) {
                        closeApplicationsModal();
                    }
                });
            }

            // Close modal with Escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    const myApplicationsModal = document.getElementById('myApplicationsModal');
                    const jobDetailsModal = document.getElementById('jobDetailsModal');
                    const applicationConfirmModal = document.getElementById('applicationConfirmModal');
                    const withdrawConfirmModal = document.getElementById('withdrawConfirmModal');

                    if (withdrawConfirmModal && withdrawConfirmModal.style.display === 'block') {
                        closeWithdrawConfirmModal();
                    } else if (applicationConfirmModal && applicationConfirmModal.style.display === 'block') {
                        closeApplicationConfirmModal();
                    } else if (myApplicationsModal && myApplicationsModal.style.display === 'block') {
                        closeApplicationsModal();
                    } else if (jobDetailsModal && jobDetailsModal.style.display === 'block') {
                        closeJobDetailsModal();
                    }
                }
            });

            // Job Details Modal Event Listeners
            const closeJobDetailsModalBtn = document.getElementById('closeJobDetailsModal');
            const jobDetailsModal = document.getElementById('jobDetailsModal');

            if (closeJobDetailsModalBtn) {
                closeJobDetailsModalBtn.addEventListener('click', function() {
                    closeJobDetailsModal();
                });
            }

            // Close modal when clicking outside
            if (jobDetailsModal) {
                jobDetailsModal.addEventListener('click', function(e) {
                    if (e.target === jobDetailsModal) {
                        closeJobDetailsModal();
                    }
                });
            }

            // Application Confirmation Modal Event Listeners
            const applicationConfirmModal = document.getElementById('applicationConfirmModal');
            if (applicationConfirmModal) {
                applicationConfirmModal.addEventListener('click', function(e) {
                    if (e.target === applicationConfirmModal) {
                        closeApplicationConfirmModal();
                    }
                });
            }

            // Withdraw Confirmation Modal Event Listeners
            const withdrawConfirmModal = document.getElementById('withdrawConfirmModal');
            if (withdrawConfirmModal) {
                withdrawConfirmModal.addEventListener('click', function(e) {
                    if (e.target === withdrawConfirmModal) {
                        closeWithdrawConfirmModal();
                    }
                });
            }

            // Filter section toggles (allow multiple open, show scrollbar if needed)
            const filterSectionHeaders = document.querySelectorAll('.filter-section-header');
            filterSectionHeaders.forEach(header => {
                const content = header.nextElementSibling;

                // Hide the availability section by default
                if (header.id === 'availabilityHeader') {
                    // Don't add active class or display block
                    content.style.display = 'none';
                }

                header.addEventListener('click', function() {
                    this.classList.toggle('active');
                    if (content.style.display === 'block') {
                        content.style.display = 'none';
                    } else {
                        content.style.display = 'block';
                    }
                });
            });

            // Toggle switch functionality
            const availabilityToggle = document.getElementById('availabilityToggle');
            const toggleStatus = document.getElementById('toggleStatus');

            availabilityToggle.addEventListener('click', function() {
                this.classList.toggle('on');
                if (this.classList.contains('on')) {
                    toggleStatus.textContent = 'ON';
                    toggleStatus.classList.remove('off');
                    toggleStatus.classList.add('on');
                } else {
                    toggleStatus.textContent = 'OFF';
                    toggleStatus.classList.remove('on');
                    toggleStatus.classList.add('off');
                }
            });

            // Custom select functionality
            function setupCustomSelect(selectId, displayId, inputId) {
                const customSelect = document.querySelector(selectId);
                const customSelectTrigger = customSelect.querySelector('.custom-select__trigger');
                const customOptions = customSelect.querySelectorAll('.custom-option');
                const displayElement = document.querySelector(displayId);
                const inputElement = document.querySelector(inputId);

                // Toggle custom select dropdown
                customSelectTrigger.addEventListener('click', function() {
                    customSelect.classList.toggle('open');

                    // Close other dropdowns when opening this one
                    document.querySelectorAll('.custom-select.open').forEach(select => {
                        if (select !== customSelect) {
                            select.classList.remove('open');
                        }
                    });

                    // Prevent body scrolling when dropdown is open
                    if (customSelect.classList.contains('open')) {
                        document.body.style.overflow = 'hidden';
                    } else {
                        document.body.style.overflow = '';
                    }
                });

                // Handle option selection
                customOptions.forEach(option => {
                    option.addEventListener('click', function() {
                        // Update display and hidden input
                        displayElement.textContent = this.textContent;
                        inputElement.value = this.getAttribute('data-value');

                        // Update selected state
                        customSelect.querySelector('.custom-option.selected').classList.remove('selected');
                        this.classList.add('selected');

                        // Close dropdown
                        customSelect.classList.remove('open');
                        document.body.style.overflow = '';

                        // Trigger filtering
                        filterJobs();
                    });
                });

                // Close custom select when clicking outside
                document.addEventListener('click', function(e) {
                    if (!customSelect.contains(e.target)) {
                        customSelect.classList.remove('open');
                        document.body.style.overflow = '';
                    }
                });

                // Close custom select when pressing Escape
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Escape' && customSelect.classList.contains('open')) {
                        customSelect.classList.remove('open');
                        document.body.style.overflow = '';
                    }
                });

                return {
                    select: customSelect,
                    display: displayElement,
                    input: inputElement,
                    options: customOptions,
                    reset: function() {
                        displayElement.textContent = customSelect.querySelector('.custom-option[data-value=""]').textContent;
                        inputElement.value = '';
                        customSelect.querySelector('.custom-option.selected').classList.remove('selected');
                        customSelect.querySelector('.custom-option[data-value=""]').classList.add('selected');
                    }
                };
            }

            // Setup custom selects
            const categorySelect = setupCustomSelect('#customCategorySelect', '#categoryDisplay', '#categoryFilter');
            const locationSelect = setupCustomSelect('#customLocationSelect', '#locationDisplay', '#locationFilter');
            const typeSelect = setupCustomSelect('#customTypeSelect', '#typeDisplay', '#typeFilter');

            // Set custom select values from URL parameters
            if (categoryParam) {
                const categoryOption = categorySelect.select.querySelector(`.custom-option[data-value="${categoryParam}"]`) ||
                                      categorySelect.select.querySelector(`.custom-option[data-value*="${categoryParam}"]`);
                if (categoryOption) {
                    categorySelect.display.textContent = categoryOption.textContent;
                    categorySelect.input.value = categoryOption.getAttribute('data-value');
                    categorySelect.select.querySelector('.custom-option.selected').classList.remove('selected');
                    categoryOption.classList.add('selected');
                }
            }

            if (locationParam) {
                const locationOption = locationSelect.select.querySelector(`.custom-option[data-value="${locationParam}"]`) ||
                                      locationSelect.select.querySelector(`.custom-option[data-value*="${locationParam}"]`);
                if (locationOption) {
                    locationSelect.display.textContent = locationOption.textContent;
                    locationSelect.input.value = locationOption.getAttribute('data-value');
                    locationSelect.select.querySelector('.custom-option.selected').classList.remove('selected');
                    locationOption.classList.add('selected');
                }
            }

            if (typeParam) {
                const typeOption = typeSelect.select.querySelector(`.custom-option[data-value="${typeParam}"]`) ||
                                  typeSelect.select.querySelector(`.custom-option[data-value*="${typeParam}"]`);
                if (typeOption) {
                    typeSelect.display.textContent = typeOption.textContent;
                    typeSelect.input.value = typeOption.getAttribute('data-value');
                    typeSelect.select.querySelector('.custom-option.selected').classList.remove('selected');
                    typeOption.classList.add('selected');
                }
            }

            // Job filtering
            const locationFilter = document.getElementById('locationFilter');
            const categoryFilter = document.getElementById('categoryFilter');
            const typeFilter = document.getElementById('typeFilter');
            const priceFilter = document.getElementById('priceFilter');
            const jobSearch = document.getElementById('jobSearch');
            const clearFiltersBtn = document.getElementById('clearFilters');
            const jobsList = document.getElementById('jobsList');
            const jobCards = document.querySelectorAll('.modern-job-card');
            const noResults = document.getElementById('noResults');

            function filterJobs() {
                const locationValue = locationFilter.value.toLowerCase();
                const categoryValue = categoryFilter.value.toLowerCase();
                const typeValue = typeFilter.value.toLowerCase();
                const priceValue = priceFilter.value ? parseInt(priceFilter.value) : 0;
                const searchValue = jobSearch.value.toLowerCase().trim();

                // Store filter values in URL parameters for pagination
                const urlParams = new URLSearchParams(window.location.search);
                if (locationValue) urlParams.set('location', locationValue);
                else urlParams.delete('location');

                if (categoryValue) urlParams.set('category', categoryValue);
                else urlParams.delete('category');

                if (typeValue) urlParams.set('type', typeValue);
                else urlParams.delete('type');

                if (priceValue) urlParams.set('price', priceValue);
                else urlParams.delete('price');

                if (searchValue) urlParams.set('search', searchValue);
                else urlParams.delete('search');

                // Update pagination links with current filters
                document.querySelectorAll('.pagination a').forEach(link => {
                    const linkUrl = new URL(link.href);
                    const pageParam = linkUrl.searchParams.get('page');

                    // Create a new URL with the current filters
                    const newUrl = new URL(window.location.origin + window.location.pathname);
                    newUrl.searchParams = urlParams;
                    if (pageParam) newUrl.searchParams.set('page', pageParam);

                    // Update the link href
                    link.href = newUrl.toString();
                });

                let visibleCount = 0;

                jobCards.forEach(card => {
                    // Basic data attributes
                    const location = card.dataset.location ? card.dataset.location.toLowerCase() : '';
                    const category = card.dataset.category ? card.dataset.category.toLowerCase() : '';
                    const type = card.dataset.type ? card.dataset.type.toLowerCase() : '';
                    const price = card.dataset.price ? parseInt(card.dataset.price) : 0;

                    // Job title and description
                    const title = card.querySelector('.job-title-container h2') ?
                                 card.querySelector('.job-title-container h2').textContent.toLowerCase() : '';
                    const description = card.querySelector('.job-description') ?
                                       card.querySelector('.job-description').textContent.toLowerCase() : '';

                    // Client information
                    const clientName = card.querySelector('.client-name-text') ?
                                      card.querySelector('.client-name-text').textContent.toLowerCase() : '';
                    const clientFullName = card.querySelector('.client-full-name') ?
                                      card.querySelector('.client-full-name').textContent.toLowerCase() : '';

                    // Get all skill tags and value-text elements for comprehensive search
                    const skillTags = Array.from(card.querySelectorAll('.skill-tag')).map(tag =>
                                     tag.textContent.toLowerCase()).join(' ');

                    // Get all value-text elements (project size, duration, experience, etc.)
                    const valueTexts = Array.from(card.querySelectorAll('.value-text')).map(el =>
                                      el.textContent.toLowerCase()).join(' ');

                    // Get all job meta items for comprehensive search
                    const metaItems = Array.from(card.querySelectorAll('.job-meta-item')).map(item =>
                                     item.textContent.toLowerCase()).join(' ');

                    // Combine all searchable text
                    const allSearchableText = `${title} ${description} ${clientName} ${clientFullName} ${skillTags} ${valueTexts} ${metaItems} ${category} ${type}`;

                    // Check if matches filters
                    const matchesLocation = !locationValue || location.includes(locationValue);
                    const matchesCategory = !categoryValue || category.includes(categoryValue);
                    const matchesType = !typeValue || type.includes(typeValue);
                    const matchesPrice = !priceValue || price >= priceValue;

                    // For search, check if any word in the search query matches
                    let matchesSearch = true;
                    if (searchValue) {
                        const searchTerms = searchValue.split(/\s+/);
                        matchesSearch = searchTerms.every(term => allSearchableText.includes(term));
                    }

                    if (matchesLocation && matchesCategory && matchesType && matchesPrice && matchesSearch) {
                        card.style.display = '';
                        visibleCount++;
                    } else {
                        card.style.display = 'none';
                    }
                });

                noResults.style.display = visibleCount === 0 ? 'block' : 'none';
            }

            // Add event listeners
            if (locationFilter) locationFilter.addEventListener('change', filterJobs);
            if (categoryFilter) categoryFilter.addEventListener('change', filterJobs);
            if (typeFilter) typeFilter.addEventListener('change', filterJobs);
            if (priceFilter) priceFilter.addEventListener('input', filterJobs);
            if (jobSearch) jobSearch.addEventListener('input', filterJobs);

            // Add search button click handler
            const jobSearchBtn = document.getElementById('jobSearchBtn');
            if (jobSearchBtn) {
                jobSearchBtn.addEventListener('click', function() {
                    filterJobs();
                    // Removed animation
                });
            }

            // Add enter key press handler for search input
            if (jobSearch) {
                jobSearch.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        filterJobs();
                        // Removed animation
                    }
                });
            }

            // Clear filters
            if (clearFiltersBtn) {
                clearFiltersBtn.addEventListener('click', function() {
                    // Reset standard filters
                    priceFilter.value = '';
                    jobSearch.value = '';

                    // Reset custom selects
                    categorySelect.reset();
                    locationSelect.reset();
                    typeSelect.reset();

                    // Clear URL parameters and redirect to first page
                    window.location.href = window.location.pathname;
                });
            }

            // Load user applications to check which jobs they've applied to
            loadUserApplicationsForButtonCheck();

        });

        // Global variable to store user's applied job IDs
        let userAppliedJobIds = new Set();

        function loadUserApplicationsForButtonCheck() {
            fetch('/my_application')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.applications) {
                        // Store all job IDs the user has applied to
                        userAppliedJobIds.clear();
                        data.applications.forEach(app => {
                            userAppliedJobIds.add(parseInt(app.job_id));
                        });

                    }
                })
                .catch(error => {
                    console.error('Error loading user applications:', error);
                });
        }
    </script>

    

    <!-- My Applications Modal -->
    <div id="myApplicationsModal" class="modal">
        <div class="modal-content applications-modal">
            <div class="modal-header applications-header">
                <h2 class="modal-title">My Applications</h2>
                <span class="close" id="closeMyApplicationsModal">&times;</span>
            </div>
            <div class="modal-body">
                <p class="applications-subtitle">View all of your job applications and their current status.</p>
                <div id="applicationsContainer" class="applications-container">
                    <!-- Applications will be loaded here -->
                    <div class="loading-applications">
                        <i class="fas fa-spinner fa-spin"></i>
                        <p>Loading your applications...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Job Details Modal -->
    <div id="jobDetailsModal" class="modal">
        <div class="modal-content job-details-modal">
            <div class="modal-header job-details-header">
                <h2 class="modal-title" id="jobDetailsTitle">Job Details</h2>
                <span class="close" id="closeJobDetailsModal">&times;</span>
            </div>
            <div class="modal-body">
                <div id="jobDetailsContainer" class="job-details-container">
                    <!-- Job details will be loaded here -->
                    <div class="loading-job-details">
                        <i class="fas fa-spinner fa-spin"></i>
                        <p>Loading job details...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Application Confirmation Modal -->
    <div id="applicationConfirmModal" class="modal" style="display: none;">
        <div class="modal-content application-confirm-modal">
            <div class="modal-header">
                <h2><i class="fas fa-paper-plane"></i> Apply for Job</h2>
                <span class="close" onclick="closeApplicationConfirmModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="confirmation-content">
                    <div class="confirmation-icon">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <h3>Are you sure you want to apply for this job?</h3>
                    <p id="confirmJobTitle" class="confirm-job-title">Job Title</p>
                    <div class="confirmation-details">
                        <p><i class="fas fa-info-circle"></i> Once you apply, the client will be notified and you cannot undo this action.</p>
                    </div>
                    <div class="confirmation-actions">
                        <button class="cancel-btn" onclick="closeApplicationConfirmModal()">
                            <i class="fas fa-times"></i> Cancel
                        </button>
                        <button class="confirm-apply-btn" id="confirmApplyBtn">
                            <i class="fas fa-paper-plane"></i> Yes, Apply Now
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Withdraw Application Confirmation Modal -->
    <div id="withdrawConfirmModal" class="modal" style="display: none;">
        <div class="modal-content withdraw-confirm-modal">
            <div class="modal-header">
                <h2><i class="fas fa-exclamation-triangle"></i> Withdraw Application</h2>
                <span class="close" onclick="closeWithdrawConfirmModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="confirmation-content">
                    <div class="confirmation-icon withdraw-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h3>Are you sure you want to withdraw this application?</h3>
                    <p id="withdrawJobTitle" class="confirm-job-title withdraw-job-title">Job Title</p>
                    <div class="confirmation-details withdraw-warning">
                        <p><i class="fas fa-exclamation-triangle"></i> This action cannot be undone. You will need to reapply if you change your mind.</p>
                    </div>
                    <div class="confirmation-actions">
                        <button class="cancel-btn" onclick="closeWithdrawConfirmModal()">
                            <i class="fas fa-times"></i> Cancel
                        </button>
                        <button class="confirm-withdraw-btn" id="confirmWithdrawBtn">
                            <i class="fas fa-trash"></i> Yes, Withdraw
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Application Status Monitoring -->
    <script>
        // Notification system
        let currentUserId = parseInt('{{ genius.id }}');

        function initializeNotificationSystem() {
            console.log('🔔 Initializing notification system for user:', currentUserId);
            // Notification system is now based on polling, no WebSocket needed
        }

        // Initialize notification system when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeNotificationSystem();

            // Initialize notification badge
            updateNotificationBadge();

            // Add notification bell click handler
            const notificationBell = document.getElementById('notification-bell');
            const notificationDropdown = document.querySelector('.notification-dropdown');

            if (notificationBell && notificationDropdown) {
                notificationBell.addEventListener('click', function(e) {
                    e.stopPropagation();

                    // Toggle dropdown
                    if (notificationDropdown.style.display === 'block') {
                        notificationDropdown.style.display = 'none';
                    } else {
                        notificationDropdown.style.display = 'block';
                        loadNotifications();

                        // Reset notification badge when user opens dropdown (reads notifications)
                        setTimeout(() => {
                            resetNotificationBadge();
                        }, 1000); // Small delay to let user see the notifications
                    }
                });

                // Close dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (!notificationBell.contains(e.target) && !notificationDropdown.contains(e.target)) {
                        notificationDropdown.style.display = 'none';
                    }
                });
            }

            // Add mark all as read handler
            const markAllRead = document.getElementById('mark-all-read');
            if (markAllRead) {
                markAllRead.addEventListener('click', function() {
                    resetNotificationBadge();

                    // Show confirmation
                    showUnifiedNotification({
                        message: 'All notifications marked as read',
                        type: 'info',
                        duration: 2000,
                        position: 'bottom-right'
                    });

                    // Close dropdown
                    notificationDropdown.style.display = 'none';
                });
            }

            // Start checking for application status updates
            startApplicationStatusMonitoring();
        });

        function loadNotifications() {
            // Load notification history from server
            fetch('/get_notification_history')
                .then(response => response.json())
                .then(data => {
                    const notificationList = document.getElementById('notification-list');
                    const emptyNotifications = document.getElementById('empty-notifications');

                    if (data.success && data.notifications && data.notifications.length > 0) {
                        notificationList.innerHTML = '';
                        emptyNotifications.style.display = 'none';

                        data.notifications.forEach(notification => {
                            const notificationItem = createNotificationItem(notification);
                            notificationList.appendChild(notificationItem);
                        });
                    } else {
                        notificationList.innerHTML = '';
                        emptyNotifications.style.display = 'block';
                    }
                })
                .catch(error => {
                    console.error('Error loading notifications:', error);
                });
        }

        function createNotificationItem(notification) {
            const item = document.createElement('div');
            item.className = 'notification-item';

            // Check for both accepted status variations
            const isAccepted = notification.status === 'accepted' || notification.status === 'accept';
            const isDeclined = notification.status === 'declined' || notification.status === 'reject';

            const statusIcon = isAccepted ?
                '<i class="fas fa-check-circle" style="color: #2ecc71;"></i>' :
                '<i class="fas fa-times-circle" style="color: #e74c3c;"></i>';

            const statusText = isAccepted ? 'Accepted' :
                              isDeclined ? 'Declined' :
                              notification.status; // fallback to actual status

            const timeAgo = formatTimeAgo(notification.updated_at || notification.created_at);

            // Build client information for the title line
            let clientNameText = '';
            if (notification.client_first_name && notification.client_last_name) {
                const clientName = `${notification.client_first_name} ${notification.client_last_name}`;
                const companyName = notification.business_name || notification.company_name;

                if (companyName && companyName !== clientName) {
                    clientNameText = ` by ${clientName} at ${companyName}`;
                } else {
                    clientNameText = ` by ${clientName}`;
                }
            } else if (notification.business_name || notification.company_name) {
                const companyName = notification.business_name || notification.company_name;
                clientNameText = ` by ${companyName}`;
            }

            console.log('🔍 Creating notification item:', {
                job_title: notification.job_title,
                status: notification.status,
                isAccepted: isAccepted,
                isDeclined: isDeclined,
                statusText: statusText,
                client_first_name: notification.client_first_name,
                client_last_name: notification.client_last_name,
                business_name: notification.business_name,
                company_name: notification.company_name
            });

            item.innerHTML = `
                <div class="notification-content">
                    <div class="notification-header">
                        ${statusIcon}
                        <span class="notification-title">Application ${statusText}${clientNameText}</span>
                    </div>
                    <div class="notification-time">${timeAgo}</div>
                </div>
            `;

            return item;
        }

        function formatTimeAgo(dateString) {
            const now = new Date();
            const date = new Date(dateString);
            const diffInSeconds = Math.floor((now - date) / 1000);

            if (diffInSeconds < 60) return 'Just now';
            if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
            if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
            return `${Math.floor(diffInSeconds / 86400)}d ago`;
        }

        // Application status monitoring
        let lastKnownStatuses = new Map();
        let notifiedStatuses = new Map(); // Track which status changes we've already notified about
        let statusCheckInterval;

        function startApplicationStatusMonitoring() {
            console.log('🔍 Starting application status monitoring...');

            // Initial load to establish baseline
            loadApplicationStatuses();

            // Check for status changes every 5 seconds for real-time notifications
            statusCheckInterval = setInterval(() => {
                checkForStatusChanges();
            }, 5000);

            // Add visual indicator that monitoring is active (only show once)
            if (!localStorage.getItem('monitoring_started_shown')) {
                showUnifiedNotification({
                    message: 'Application status monitoring is now active. You\'ll be notified of any updates!',
                    type: 'info',
                    title: 'Monitoring Started',
                    duration: 4000
                });
                localStorage.setItem('monitoring_started_shown', 'true');
            }
        }

        function loadApplicationStatuses() {
            fetch('/my_application')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.applications) {
                        console.log('📊 Loaded application statuses:', data.applications.length);

                        // Store current statuses and mark them as already notified (initial load)
                        data.applications.forEach(app => {
                            const statusKey = `${app.job_id}_${app.status}`;

                            lastKnownStatuses.set(app.job_id, {
                                status: app.status,
                                job_title: app.job_title,
                                updated_at: app.updated_at
                            });

                            // Mark current statuses as already notified to prevent initial notifications
                            notifiedStatuses.set(statusKey, true);
                        });

                        console.log('💾 Stored statuses for', lastKnownStatuses.size, 'applications');
                        console.log('🔕 Marked', notifiedStatuses.size, 'statuses as already notified');
                    }
                })
                .catch(error => {
                    console.error('❌ Error loading application statuses:', error);
                });
        }

        function checkForStatusChanges() {
            const now = new Date().toLocaleTimeString();
            console.log(`🔄 [${now}] Checking for application status changes...`);

            fetch('/my_application')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.applications) {
                        let hasNewNotifications = false;

                        data.applications.forEach(app => {
                            const lastKnown = lastKnownStatuses.get(app.job_id);
                            const statusKey = `${app.job_id}_${app.status}`;

                            // Debug logging
                            console.log(`📋 Job ${app.job_id}: "${app.job_title}" - Current: "${app.status}", Last Known: "${lastKnown?.status}"`);

                            if (lastKnown) {
                                // Check if status changed to accepted
                                if (lastKnown.status !== 'accepted' && lastKnown.status !== 'accept' &&
                                    (app.status === 'accepted' || app.status === 'accept')) {

                                    // Only notify if we haven't notified about this specific status change
                                    if (!notifiedStatuses.has(statusKey)) {
                                        console.log('🎉 NEW Application accepted!', app.job_title);

                                        // Build client info for notification
                                        let clientInfo = '';
                                        if (app.client_first_name && app.client_last_name) {
                                            const clientName = `${app.client_first_name} ${app.client_last_name}`;
                                            const companyName = app.company_name;

                                            if (companyName && companyName !== clientName) {
                                                clientInfo = ` by ${clientName} at ${companyName}`;
                                            } else {
                                                clientInfo = ` by ${clientName}`;
                                            }
                                        } else if (app.company_name) {
                                            clientInfo = ` by ${app.company_name}`;
                                        }

                                        // Show clean notification popup in bottom-right
                                        showUnifiedNotification({
                                            message: `Application accepted for "${app.job_title}"${clientInfo}`,
                                            type: 'success',
                                            title: 'Application Accepted',
                                            duration: 6000,
                                            position: 'bottom-right'
                                        });

                                        // Increment notification badge
                                        incrementNotificationBadge();

                                        // Mark this status change as notified
                                        notifiedStatuses.set(statusKey, true);
                                        hasNewNotifications = true;
                                    } else {
                                        console.log('🔕 Already notified about accepted status for', app.job_title);
                                    }
                                }

                                // Check if status changed to declined
                                else if (lastKnown.status !== 'declined' && lastKnown.status !== 'reject' &&
                                        (app.status === 'declined' || app.status === 'reject')) {

                                    // Only notify if we haven't notified about this specific status change
                                    if (!notifiedStatuses.has(statusKey)) {
                                        console.log('❌ NEW Application declined:', app.job_title);

                                        // Build client info for notification
                                        let clientInfo = '';
                                        if (app.client_first_name && app.client_last_name) {
                                            const clientName = `${app.client_first_name} ${app.client_last_name}`;
                                            const companyName = app.company_name;

                                            if (companyName && companyName !== clientName) {
                                                clientInfo = ` by ${clientName} at ${companyName}`;
                                            } else {
                                                clientInfo = ` by ${clientName}`;
                                            }
                                        } else if (app.company_name) {
                                            clientInfo = ` by ${app.company_name}`;
                                        }

                                        // Show notification popup
                                        showUnifiedNotification({
                                            message: `Application declined for "${app.job_title}"${clientInfo}`,
                                            type: 'warning',
                                            title: 'Application Update',
                                            duration: 6000,
                                            position: 'bottom-right'
                                        });

                                        // Increment notification badge
                                        incrementNotificationBadge();

                                        // Mark this status change as notified
                                        notifiedStatuses.set(statusKey, true);
                                        hasNewNotifications = true;
                                    } else {
                                        console.log('🔕 Already notified about declined status for', app.job_title);
                                    }
                                }
                            }

                            // Update stored status
                            lastKnownStatuses.set(app.job_id, {
                                status: app.status,
                                job_title: app.job_title,
                                updated_at: app.updated_at
                            });
                        });

                        if (hasNewNotifications) {
                            console.log('🔔 NEW application status changes detected and notified!');
                        } else {
                            console.log('✅ No new status changes to notify about');
                        }
                    }
                })
                .catch(error => {
                    console.error('❌ Error checking status changes:', error);
                });
        }

        // Stop monitoring when page is unloaded
        window.addEventListener('beforeunload', function() {
            if (statusCheckInterval) {
                clearInterval(statusCheckInterval);
            }
        });

        // Function to reset notification tracking (useful for testing or logout)
        function resetNotificationTracking() {
            notifiedStatuses.clear();
            console.log('🔄 Notification tracking reset');
        }
    </script>

    </body>
</html>