<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Affiliate Dashboard | GigGenius</title>
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='img/logo.png') }}">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #0A66C2;
            --primary-pink: #FF5A5F;
            --light-gray: #F5F5F5;
            --medium-gray: #E0E0E0;
            --dark-gray: #757575;
            --text-color: #333333;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background-color: var(--light-gray);
            color: var(--text-color);
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0;
        }
        
        /* Navbar Styles */
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 2rem;
            background-color: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .logo img {
            width: 3rem;
            height: 3rem;
            border-radius: 50%;
        }
        
        .logo h1 {
            font-size: 1.5rem;
            color: var(--primary-blue);
            font-weight: 600;
        }
        
        .nav-links {
            display: flex;
            gap: 2rem;
        }
        
        .nav-links a {
            color: var(--text-color);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        
        .nav-links a:hover, .nav-links a.active {
            color: var(--primary-pink);
        }
        
        .navbar-right {
            display: flex;
            align-items: center;
            gap: 2rem;
        }
        
        /* Dashboard Styles */
        main {
            padding: 2rem;
        }

    </style>
</head>
<body>
    <div class="container">
        <!-- Navbar -->
        <nav class="navbar">
            <div style="display: flex; align-items: center;">
                <div class="logo">
                    <img src="{{ url_for('static', filename='img/giggenius_logo.jpg') }}" alt="GigGenius Logo">
                    <h1>GigGenius</h1>
                </div>
                <div class="nav-links">
                    <a href="#" class="active">Dashboard</a>
                    <a href="#">Referrals</a>
                    <a href="#">Earnings</a>
                    <a href="#">Resources</a>
                </div>
            </div>
            <div class="navbar-right">
                    <div class="profile-dropdown-content">
                        <a href="#">
                            <i class="fas fa-user"></i>
                            My Profile
                        </a>
                        <a href="#">
                            <i class="fas fa-cog"></i>
                            Settings
                        </a>
                        <a href="{{ url_for('logout') }}">
                            <i class="fas fa-sign-out-alt"></i>
                            Logout
                        </a>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main>
            <div class="dashboard-header">
                <div class="welcome-section">
                    <div class="welcome-text">
                        <h1>Welcome!</h1>
                        <p>Your Affiliate Dashboard</p>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>

    </script>
</body>
</html>