from flask import request, jsonify, session
import os
import uuid
import mimetypes
from werkzeug.utils import secure_filename
import base64
from datetime import datetime

def handle_file_upload(app, socketio, get_db_connection):
    """
    Set up file upload routes and Socket.IO event handlers
    """

    # Maximum file size (5MB)
    MAX_FILE_SIZE = 5 * 1024 * 1024

    # Configure allowed file extensions
    ALLOWED_EXTENSIONS = {
        'image': ['jpg', 'jpeg', 'png', 'gif', 'webp'],
        'document': ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'],
        'archive': ['zip', 'rar', '7z'],
        'other': ['csv', 'json']
    }

    def allowed_file(filename):
        """Check if the file extension is allowed"""
        if '.' not in filename:
            return False
        ext = filename.rsplit('.', 1)[1].lower()
        all_allowed = []
        for category in ALLOWED_EXTENSIONS.values():
            all_allowed.extend(category)
        return ext in all_allowed

    def get_file_category(filename):
        """Get the category of the file based on extension"""
        if '.' not in filename:
            return 'other'
        ext = filename.rsplit('.', 1)[1].lower()
        for category, extensions in ALLOWED_EXTENSIONS.items():
            if ext in extensions:
                return category
        return 'other'

    @app.route('/api/upload_file', methods=['POST'])
    def upload_file():
        """Handle file uploads via HTTP POST"""
        if 'file' not in request.files:
            return jsonify(success=False, error="No file part")

        file = request.files['file']

        if file.filename == '':
            return jsonify(success=False, error="No selected file")

        if not allowed_file(file.filename):
            return jsonify(success=False, error="File type not allowed")

        # Check file size
        file.seek(0, os.SEEK_END)
        file_size = file.tell()
        file.seek(0)

        if file_size > MAX_FILE_SIZE:
            return jsonify(success=False, error=f"File too large (max {MAX_FILE_SIZE/1024/1024}MB)")

        # Read file data
        file_data = file.read()

        # Get MIME type using mimetypes module
        mime_type, _ = mimetypes.guess_type(file.filename)
        if not mime_type:
            # Default to application/octet-stream if type can't be determined
            mime_type = 'application/octet-stream'

        # Generate a secure filename
        filename = secure_filename(file.filename)

        # Store file info in session for later use with Socket.IO
        file_info = {
            'filename': filename,
            'mime_type': mime_type,
            'data': base64.b64encode(file_data).decode('utf-8'),
            'size': file_size
        }

        return jsonify(success=True, file_info=file_info)

    # Note: The Socket.IO handler for file messages is now defined in app.py
    # This avoids duplication and potential conflicts
